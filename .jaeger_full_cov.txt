============================= test session starts ==============================
platform darwin -- Python 3.13.2, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Jaeger/llm_env/bin/python3
cachedir: .pytest_cache
rootdir: /Users/<USER>/Jaeger
configfile: pytest.ini
plugins: cov-6.2.1
collecting ... collected 764 items

tests/test__broker.py::test_broker_initialization PASSED                 [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_analyze_market_regime PASSED [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_fail_fast_on_missing_data PASSED [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_generate_situational_discovery_prompt_real_data PASSED [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_generate_situational_validation_prompt_real_data PASSED [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_get_core_situational_questions PASSED [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_get_situational_categories PASSED [  0%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_get_tom_hougaard_examples PASSED [  1%]
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_no_hardcoded_params PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_class_instantiation PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_get_available_models_failfast PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_get_working_model_failfast PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_is_server_running_failfast PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_main_function_exists PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_model_loading_failfast PASSED [  1%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_no_hardcoded_params PASSED [  2%]
tests/test_ai_integration_lm_studio_client.py::TestLMStudioClient::test_send_message_failfast PASSED [  2%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_analyze_market_regime_branches PASSED [  2%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_analyze_market_regime_real_data PASSED [  2%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_generate_discovery_prompt_missing_ohlc PASSED [  2%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_generate_discovery_prompt_real_data PASSED [  2%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_get_core_situational_questions PASSED [  2%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_get_tom_hougaard_examples PASSED [  3%]
tests/test_ai_integration_situational_prompts.py::TestSituationalPrompts::test_no_hardcoded_params PASSED [  3%]
tests/test_backtesting___init__.py::TestBacktestingInit::test_import PASSED [  3%]
tests/test_backtesting___init__.py::TestBacktestingInit::test_module_imports PASSED [  3%]
tests/test_backtesting___init__.py::TestBacktestingInit::test_pool_function_non_spawn_method PASSED [  3%]
tests/test_backtesting___init__.py::TestBacktestingInit::test_pool_function_spawn_method PASSED [  3%]
tests/test_backtesting___init__.py::TestBacktestingInit::test_version_attribute_exists PASSED [  3%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_basic_run_and_stats PASSED [  3%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_empty_data PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_invalid_commission_type PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_invalid_data_type PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_invalid_spread_type PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_invalid_strategy_type PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_missing_column PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_fail_on_nan_ohlc PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_finalize_trades_logic PASSED [  4%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_indicator_slicing_and_2d PASSED [  5%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_out_of_money_error_branch PASSED [  5%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_warn_on_non_datetime_index PASSED [  5%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_warn_on_price_greater_than_cash PASSED [  5%]
tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_warn_on_unsorted_index PASSED [  5%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_adjusted_price PASSED [  5%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_broker_init_and_repr PASSED [  5%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_broker_order_and_trade_flow PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_commission_logic PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_equity_and_margin_properties PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_fail_on_invalid_cash_or_margin PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_fail_on_nan_in_ohlc PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_new_order_fail_fast PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerJaegerCompliance::test_out_of_money_triggers PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerDirectCoverage::test_direct_broker_usage PASSED [  6%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_commission_callable PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_contingent_order_closes_trade PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_exclusive_orders PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_hedging_branch PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_limit_order_not_hit PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_out_of_money_error PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_pessimistic_limit_before_stop PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_relative_size_order_and_margin_warning PASSED [  7%]
tests/test_backtesting__broker.py::TestBrokerCoverageRaise::test_stop_order_not_hit PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_commission_callable PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_contingent_order_closes_trade PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_exclusive_orders PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_exclusive_orders_cancel_orders PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_fifo_closing_opposite_trade PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_hedging_branch PASSED [  8%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_limit_order_not_hit PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_long_order_sl_limit_tp_logic PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_out_of_money_error PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_partial_reduce_existing_trade PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_relative_size_order_and_margin_warning PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_short_order_tp_limit_sl_logic PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_stop_and_limit_same_bar_pessimistic PASSED [  9%]
tests/test_backtesting__broker_extra.py::TestBrokerExtraCoverage::test_stop_order_not_hit PASSED [  9%]
tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_plot PASSED [ 10%]
tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_plot_edge_cases PASSED [ 10%]
tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_plot_heatmaps PASSED [ 10%]
tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_plot_heatmaps_with_actual_plotting PASSED [ 10%]
tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_plot_with_equity_curve PASSED [ 10%]
tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_set_bokeh_output PASSED [ 10%]
tests/test_backtesting__stats.py::TestBacktestingStatsJaeger::test_compute_stats_no_trades PASSED [ 10%]
tests/test_backtesting__stats.py::TestBacktestingStatsJaeger::test_compute_stats_with_realistic_trades PASSED [ 10%]
tests/test_backtesting__stats.py::TestBacktestingStatsJaeger::test_dummy_stats PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_array_and_indicator PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_as_str PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_batch PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_data_container PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_patch_context_manager PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_shared_memory_manager PASSED [ 11%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_strategy_indicators_and_warmup PASSED [ 12%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_tqdm_fallback PASSED [ 12%]
tests/test_backtesting__util.py::TestBacktestingUtilJaeger::test_try_success_and_fail PASSED [ 12%]
tests/test_backtesting_backtesting.py::TestBacktestingModuleDirect::test_position_order_trade_classes PASSED [ 12%]
tests/test_backtesting_backtesting.py::TestBacktestingModuleDirect::test_strategy_class_direct PASSED [ 12%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_buy_sell_position_orders_trades PASSED [ 12%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_fail_fast_on_nan PASSED [ 12%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_indicator_declaration_and_overlay SKIPPED [ 12%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_order_properties_and_cancel PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_order_repr_and_flags PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_position_bool_and_repr PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_position_properties_and_close PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_real_data_compliance_and_run PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_strategy_I_invalid_name_type PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_strategy_I_overlay_and_errors PASSED [ 13%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_strategy_buy_sell_invalid_size PASSED [ 14%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_strategy_check_params_error SKIPPED [ 14%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_strategy_param_check_and_repr PASSED [ 14%]
tests/test_backtesting_backtesting.py::TestBacktestingCompliance::test_trade_properties_and_setters PASSED [ 14%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_barssince PASSED [ 14%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_compute_stats_wrapper SKIPPED [ 14%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_cross_and_crossover PASSED [ 14%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_ohlcv_agg_and_trades_agg PASSED [ 14%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_plot_heatmaps_wrapper PASSED [ 15%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_quantile PASSED [ 15%]
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_resample_apply PASSED [ 15%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_calculate_stop_price_methods PASSED [ 15%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_calculate_target_price_methods PASSED [ 15%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_direction_normalization_and_validation PASSED [ 15%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_edge_cases_and_error_handling PASSED [ 15%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_entry_condition_evaluation_comprehensive PASSED [ 15%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_generate_python_functions PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_module_import PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_no_fallback_violation PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_no_hardcoded_params PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_none_price_handling PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_numeric_parsing_and_defaults PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_parse_backtesting_rules_function PASSED [ 16%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_parse_missing_required_fields PASSED [ 17%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_parse_valid_llm_response PASSED [ 17%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_rule_function_execution_with_real_data PASSED [ 17%]
tests/test_backtesting_rule_parser.py::TestBacktestingRuleParser::test_rule_validation_logic PASSED [ 17%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_add_behavioral_intelligence PASSED [ 17%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_add_behavioral_intelligence_error_handling PASSED [ 17%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_all_timeframe_bars_per_day PASSED [ 17%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_behavioral_intelligence_exception_recovery PASSED [ 17%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_behavioral_summaries_comprehensive PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_config_simplified_mode_true PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_datetime_index_conversion_failure PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_day_of_week_invalid_string_mapping PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_day_of_week_string_mapping PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_empty_data_handling PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_error_handling_invalid_datetime_index PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_full_behavioral_intelligence_features PASSED [ 18%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_generate_behavioral_summaries PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_generate_behavioral_summaries_edge_cases PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_generate_clean_timeframes PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_generate_clean_timeframes_edge_cases PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_get_bars_per_day_helper PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_infinite_and_nan_cleanup PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_missing_columns_error PASSED [ 19%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_missing_day_of_week_column PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_missing_volume_column PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_pattern_recognition PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_simplified_behavioral_intelligence PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_timeframe_classification PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_timeframe_generation_exception_handling PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_volume_handling_with_nan PASSED [ 20%]
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_volume_missing_handling PASSED [ 20%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_chart_with_error_handling PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_format_performance_metrics PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_format_performance_metrics_error_handling PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_backtest_html_chart_failfast_missing_col PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_backtest_html_chart_missing_attributes PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_backtest_html_chart_no_trades PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_backtest_html_chart_success PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_optimization_heatmap PASSED [ 21%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_optimization_heatmap_empty_results PASSED [ 22%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_generate_optimization_heatmap_none_results PASSED [ 22%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_instantiation PASSED [ 22%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_output_directory_creation PASSED [ 22%]
tests/test_chart_html_generator.py::TestChartHTMLGenerator::test_real_data_validation PASSED [ 22%]
tests/test_config.py::TestConfig::test_backtesting_configuration PASSED  [ 22%]
tests/test_config.py::TestConfig::test_backtesting_execution_configuration PASSED [ 22%]
tests/test_config.py::TestConfig::test_behavioral_intelligence_configuration PASSED [ 23%]
tests/test_config.py::TestConfig::test_boolean_conversion_in_load_from_file PASSED [ 23%]
tests/test_config.py::TestConfig::test_config_attribute_access PASSED    [ 23%]
tests/test_config.py::TestConfig::test_config_immutability_after_init PASSED [ 23%]
tests/test_config.py::TestConfig::test_data_processing_configuration PASSED [ 23%]
tests/test_config.py::TestConfig::test_dynamic_criteria_configuration PASSED [ 23%]
tests/test_config.py::TestConfig::test_env_loading_and_fields PASSED     [ 23%]
tests/test_config.py::TestConfig::test_fail_fast_on_missing_env PASSED   [ 23%]
tests/test_config.py::TestConfig::test_file_paths_configuration PASSED   [ 24%]
tests/test_config.py::TestConfig::test_lm_studio_configuration PASSED    [ 24%]
tests/test_config.py::TestConfig::test_load_from_file_functionality PASSED [ 24%]
tests/test_config.py::TestConfig::test_load_from_file_invalid_lines PASSED [ 24%]
tests/test_config.py::TestConfig::test_load_from_file_method PASSED      [ 24%]
tests/test_config.py::TestConfig::test_load_from_file_nonexistent_file PASSED [ 24%]
tests/test_config.py::TestConfig::test_load_from_file_with_type_conversion PASSED [ 24%]
tests/test_config.py::TestConfig::test_load_from_nonexistent_file PASSED [ 25%]
tests/test_config.py::TestConfig::test_load_jaeger_env_file_not_found PASSED [ 25%]
tests/test_config.py::TestConfig::test_logging_configuration PASSED      [ 25%]
tests/test_config.py::TestConfig::test_mt4_configuration PASSED          [ 25%]
tests/test_config.py::TestConfig::test_save_to_file_functionality PASSED [ 25%]
tests/test_config.py::TestConfig::test_save_to_file_method PASSED        [ 25%]
tests/test_config.py::TestConfig::test_situational_analysis_configuration PASSED [ 25%]
tests/test_config.py::TestConfig::test_to_dict_functionality PASSED      [ 25%]
tests/test_config.py::TestConfig::test_to_dict_method PASSED             [ 26%]
tests/test_config.py::TestConfig::test_trading_configuration PASSED      [ 26%]
tests/test_config.py::TestConfig::test_update_from_dict_functionality PASSED [ 26%]
tests/test_config.py::TestConfig::test_update_from_dict_method PASSED    [ 26%]
tests/test_config.py::TestConfig::test_v2_validation_configuration PASSED [ 26%]
tests/test_config.py::TestConfig::test_validation_configuration PASSED   [ 26%]
tests/test_config.py::TestConfig::test_walk_forward_configuration PASSED [ 26%]
tests/test_cortex.py::TestCortex::test_add_behavioral_context PASSED     [ 26%]
tests/test_cortex.py::TestCortex::test_add_behavioral_context_error_handling PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_pattern_characteristics PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_pattern_characteristics_empty_data PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_pattern_characteristics_missing_fields PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_performance_insights PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_performance_insights_duplicate PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_performance_insights_empty_data PASSED [ 27%]
tests/test_cortex.py::TestCortex::test_aggregate_validation_metrics PASSED [ 28%]
tests/test_cortex.py::TestCortex::test_aggregate_validation_metrics_empty_data PASSED [ 28%]
tests/test_cortex.py::TestCortex::test_aggregate_validation_metrics_missing_fields PASSED [ 28%]
tests/test_cortex.py::TestCortex::test_analyze_timeframe_behavior PASSED [ 28%]
tests/test_cortex.py::TestCortex::test_autonomous_llm_analysis PASSED    [ 28%]
tests/test_cortex.py::TestCortex::test_autonomous_llm_analysis_runtime_error PASSED [ 28%]
tests/test_cortex.py::TestCortex::test_autonomous_llm_analysis_with_feedback PASSED [ 28%]
tests/test_cortex.py::TestCortex::test_cleanup_old_sessions PASSED       [ 28%]
tests/test_cortex.py::TestCortex::test_cleanup_old_sessions_no_directory PASSED [ 29%]
tests/test_cortex.py::TestCortex::test_cleanup_old_sessions_nonexistent_dir PASSED [ 29%]
tests/test_cortex.py::TestCortex::test_cleanup_old_sessions_removes_excess PASSED [ 29%]
tests/test_cortex.py::TestCortex::test_cortex_initialization PASSED      [ 29%]
tests/test_cortex.py::TestCortex::test_cortex_initialization_attributes PASSED [ 29%]
tests/test_cortex.py::TestCortex::test_determine_quality_rating PASSED   [ 29%]
tests/test_cortex.py::TestCortex::test_discover_patterns_backtesting_rule_parse_error PASSED [ 29%]
tests/test_cortex.py::TestCortex::test_discover_patterns_data_loading_failure PASSED [ 29%]
tests/test_cortex.py::TestCortex::test_discover_patterns_file_not_found PASSED [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_file_validation PASSED [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_integration PASSED [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_learning_disabled PASSED [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_llm_not_running PASSED [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_missing_file PASSED [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_no_file PASSED  [ 30%]
tests/test_cortex.py::TestCortex::test_discover_patterns_rule_parsing_failure PASSED [ 31%]
tests/test_cortex.py::TestCortex::test_discover_patterns_server_not_running PASSED [ 31%]
tests/test_cortex.py::TestCortex::test_discover_patterns_success PASSED  [ 31%]
tests/test_cortex.py::TestCortex::test_discover_patterns_success_flow PASSED [ 31%]
tests/test_cortex.py::TestCortex::test_discover_patterns_unexpected_error PASSED [ 31%]
tests/test_cortex.py::TestCortex::test_discover_patterns_unsupported_format PASSED [ 31%]
tests/test_cortex.py::TestCortex::test_edge_cases PASSED                 [ 31%]
tests/test_cortex.py::TestCortex::test_extract_enhanced_learning_data PASSED [ 31%]
tests/test_cortex.py::TestCortex::test_extract_enhanced_learning_data_excellent_quality PASSED [ 32%]
tests/test_cortex.py::TestCortex::test_extract_enhanced_learning_data_fair_quality PASSED [ 32%]
tests/test_cortex.py::TestCortex::test_extract_individual_patterns_function PASSED [ 32%]
tests/test_cortex.py::TestCortex::test_extract_insight_keywords PASSED   [ 32%]
tests/test_cortex.py::TestCortex::test_extract_market_context PASSED     [ 32%]
tests/test_cortex.py::TestCortex::test_extract_pattern_characteristics PASSED [ 32%]
tests/test_cortex.py::TestCortex::test_extract_symbol_from_filename PASSED [ 32%]
tests/test_cortex.py::TestCortex::test_extract_symbol_from_filename_invalid PASSED [ 32%]
tests/test_cortex.py::TestCortex::test_extract_symbol_from_filename_valid PASSED [ 33%]
tests/test_cortex.py::TestCortex::test_generate_equity_chart PASSED      [ 33%]
tests/test_cortex.py::TestCortex::test_generate_equity_chart_empty_data PASSED [ 33%]
tests/test_cortex.py::TestCortex::test_generate_equity_chart_invalid_entry_time PASSED [ 33%]
tests/test_cortex.py::TestCortex::test_generate_equity_chart_no_data PASSED [ 33%]
tests/test_cortex.py::TestCortex::test_generate_equity_chart_no_equity_data PASSED [ 33%]
tests/test_cortex.py::TestCortex::test_generate_equity_chart_with_data PASSED [ 33%]
tests/test_cortex.py::TestCortex::test_generate_learning_intelligence PASSED [ 34%]
tests/test_cortex.py::TestCortex::test_generate_learning_intelligence_empty_data PASSED [ 34%]
tests/test_cortex.py::TestCortex::test_generate_learning_intelligence_missing_fields PASSED [ 34%]
tests/test_cortex.py::TestCortex::test_generate_performance_feedback_context PASSED [ 34%]
tests/test_cortex.py::TestCortex::test_generate_performance_feedback_context_empty PASSED [ 34%]
tests/test_cortex.py::TestCortex::test_generate_timeframe_data PASSED    [ 34%]
tests/test_cortex.py::TestCortex::test_generate_trading_system PASSED    [ 34%]
tests/test_cortex.py::TestCortex::test_get_next_gipsy_danger_number PASSED [ 34%]
tests/test_cortex.py::TestCortex::test_get_next_gipsy_danger_number_existing_counter PASSED [ 35%]
tests/test_cortex.py::TestCortex::test_get_next_gipsy_danger_number_invalid_counter PASSED [ 35%]
tests/test_cortex.py::TestCortex::test_get_next_gipsy_danger_number_new_counter PASSED [ 35%]
tests/test_cortex.py::TestCortex::test_get_next_gipsy_danger_number_no_counter PASSED [ 35%]
tests/test_cortex.py::TestCortex::test_import PASSED                     [ 35%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data PASSED      [ 35%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_csv PASSED  [ 35%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_csv_existing PASSED [ 35%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_errors PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_excel PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_missing_datetime PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_missing_file PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_missing_file_existing PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_missing_required_column PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_and_prepare_data_with_date_time_columns PASSED [ 36%]
tests/test_cortex.py::TestCortex::test_load_previous_feedback PASSED     [ 37%]
tests/test_cortex.py::TestCortex::test_load_previous_feedback_empty PASSED [ 37%]
tests/test_cortex.py::TestCortex::test_load_previous_feedback_no_directory PASSED [ 37%]
tests/test_cortex.py::TestCortex::test_main_function PASSED              [ 37%]
tests/test_cortex.py::TestCortex::test_main_function_coverage PASSED     [ 37%]
tests/test_cortex.py::TestCortex::test_main_function_no_data_dir PASSED  [ 37%]
tests/test_cortex.py::TestCortex::test_main_function_no_data_files PASSED [ 37%]
tests/test_cortex.py::TestCortex::test_main_function_success PASSED      [ 37%]
tests/test_cortex.py::TestCortex::test_missing_coverage_lines PASSED     [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_backtesting PASSED    [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_backtesting_profitable PASSED [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_file_generation PASSED [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_file_generation_no_profitable_patterns PASSED [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_file_generation_profitable PASSED [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_file_generation_unprofitable PASSED [ 38%]
tests/test_cortex.py::TestCortex::test_orchestrate_file_generation_with_profitable_patterns PASSED [ 39%]
tests/test_cortex.py::TestCortex::test_pattern_execution_error_handling PASSED [ 39%]
tests/test_cortex.py::TestCortex::test_pattern_strategy_initialization PASSED [ 39%]
tests/test_cortex.py::TestCortex::test_pattern_strategy_next_method PASSED [ 39%]
tests/test_cortex.py::TestCortex::test_run_backtest_analysis PASSED      [ 39%]
tests/test_cortex.py::TestCortex::test_run_single_pattern_backtest_comprehensive PASSED [ 39%]
tests/test_cortex.py::TestCortex::test_save_llm_feedback PASSED          [ 39%]
tests/test_cortex.py::TestCortex::test_save_llm_feedback_success PASSED  [ 39%]
tests/test_cortex.py::TestCortex::test_validate_order_parameters PASSED  [ 40%]
tests/test_cortex.py::TestCortex::test_validate_order_parameters_direction_detection PASSED [ 40%]
tests/test_cortex.py::TestCortex::test_validate_order_parameters_long PASSED [ 40%]
tests/test_cortex.py::TestCortex::test_validate_order_parameters_missing PASSED [ 40%]
tests/test_cortex.py::TestCortex::test_validate_order_parameters_missing_params PASSED [ 40%]
tests/test_cortex.py::TestCortex::test_validate_order_parameters_short PASSED [ 40%]
tests/test_data_ingestion.py::TestDataIngestion::test_bid_ask_format_conversion PASSED [ 40%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_aggregation PASSED [ 40%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_caching PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_export PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_filtering PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_format_conversion PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_ingestion_manager_init PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_integrity_checks PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_preprocessing PASSED [ 41%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_source_handling PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_summary_comprehensive PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_types_validation PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_data_validation_edge_cases PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_datetime_column_synthesis_from_lowercase PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_duplicate_timestamps_removal PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_excel_file_loading PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_file_handling_errors PASSED [ 42%]
tests/test_data_ingestion.py::TestDataIngestion::test_get_data_summary PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_import PASSED      [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_data_file_csv_path_specifically PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_data_file_excel PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_data_file_none_return PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_data_file_xlsx_format PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_market_data_csv PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_market_data_file_not_found PASSED [ 43%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_market_data_insufficient_data PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_load_market_data_unsupported_format PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_missing_data_handling_in_validation PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_no_hardcoded_params PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_prepare_for_backtesting PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_prepare_for_backtesting_non_datetime_index PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_real_data_only PASSED [ 44%]
tests/test_data_ingestion.py::TestDataIngestion::test_real_data_structure PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_strict_ohlc_capitalization PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_and_prepare_data PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_and_prepare_data_missing_columns PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_and_prepare_data_missing_datetime PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_and_prepare_data_missing_datetime_no_fallback PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_ohlc_integrity PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_ohlc_integrity_invalid_relationships PASSED [ 45%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_ohlc_integrity_negative_prices PASSED [ 46%]
tests/test_data_ingestion.py::TestDataIngestion::test_validate_ohlc_integrity_with_missing_values_and_fixes PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_clean PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_frequency_day PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_frequency_month PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_r_average PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_r_multiple PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_success_rate PASSED [ 46%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_fabricated_metrics_win_rate PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_time_claims_invalid PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_time_claims_valid PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_volume_claims_invalid PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_volume_claims_no_volume_column PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_check_volume_claims_valid PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_data_column_renaming_coverage PASSED [ 47%]
tests/test_fact_checker.py::TestLLMFactChecker::test_datetime_processing_coverage PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_edge_cases_empty_response PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_edge_cases_numeric_response PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_example_usage_section PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_full_validation_workflow_with_metrics PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_full_validation_workflow_with_volume PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_generate_validation_report_clean PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_generate_validation_report_errors_and_warnings PASSED [ 48%]
tests/test_fact_checker.py::TestLLMFactChecker::test_generate_validation_report_errors_only PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_generate_validation_report_warnings_only PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_initialization PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_main_function_coverage PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_main_function_no_data_file PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_multiple_time_claims PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_multiple_volume_claims PASSED [ 49%]
tests/test_fact_checker.py::TestLLMFactChecker::test_real_data_integrity PASSED [ 50%]
tests/test_fact_checker.py::TestLLMFactChecker::test_validate_response_clean PASSED [ 50%]
tests/test_fact_checker.py::TestLLMFactChecker::test_validation_state_reset PASSED [ 50%]
tests/test_fact_checker.py::TestLLMFactChecker::test_volume_claims_with_commas PASSED [ 50%]
tests/test_fact_checker.py::TestLLMFactChecker::test_volume_tolerance_boundary PASSED [ 50%]
tests/test_file_generator.py::TestFileGenerator::test_edge_cases_and_error_handling PASSED [ 50%]
tests/test_file_generator.py::TestFileGenerator::test_file_generator_initialization PASSED [ 50%]
tests/test_file_generator.py::TestFileGenerator::test_format_backtesting_py_stats PASSED [ 50%]
tests/test_file_generator.py::TestFileGenerator::test_generate_html_charts PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_html_charts_chart_gen_exception PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_html_charts_no_backtest PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_html_charts_plot_method_failure PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_html_charts_with_plot_method PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_backup_existing PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_files PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_files_no_backtest PASSED [ 51%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_files_no_trades PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_general_exception PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_missing_columns PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trade_csv_write_error PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trading_system_files_complete PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trading_system_files_no_backtest PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trading_system_report PASSED [ 52%]
tests/test_file_generator.py::TestFileGenerator::test_generate_trading_system_report_no_backtest PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_generate_validated_mt4_ea_file_write_error PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_generate_validated_mt4_ea_no_profitable_patterns PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_generate_validated_mt4_ea_no_validation PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_generate_validated_mt4_ea_success PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_import PASSED      [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_save_empty_mt4_ea PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_save_empty_mt4_ea_file_write_error PASSED [ 53%]
tests/test_file_generator.py::TestFileGenerator::test_save_mt4_ea_file PASSED [ 54%]
tests/test_file_generator.py::TestFileGenerator::test_save_mt4_ea_file_backup_existing PASSED [ 54%]
tests/test_file_generator.py::TestFileGenerator::test_save_mt4_ea_file_no_code PASSED [ 54%]
tests/test_file_generator.py::TestFileGenerator::test_save_mt4_ea_file_write_error PASSED [ 54%]
tests/test_legacy_situational_prompts.py::test_fail_on_missing_real_data PASSED [ 54%]
tests/test_legacy_situational_prompts.py::test_get_core_situational_questions PASSED [ 54%]
tests/test_legacy_situational_prompts.py::test_get_tom_hougaard_examples PASSED [ 54%]
tests/test_legacy_situational_prompts.py::test_get_situational_categories PASSED [ 54%]
tests/test_legacy_situational_prompts.py::test_generate_situational_discovery_prompt_and_analyze_market_regime PASSED [ 55%]
tests/test_legacy_situational_prompts.py::test_generate_situational_validation_prompt PASSED [ 55%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_behavioral_stability_with_multiple_periods PASSED [ 55%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_behavioral_stability_with_no_time_column PASSED [ 55%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_calculate_profit_factor_no_trades PASSED [ 55%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_calculate_profit_factor_only_losers PASSED [ 55%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_calculate_profit_factor_only_winners PASSED [ 55%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_calculate_profit_factor_positive PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_calculate_situation_frequency PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_calculate_situation_frequency_empty PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_consistency_validation_with_no_hourly_data PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_consistency_with_hourly_performance_analysis PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_context_performance_normal_volatility PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_context_performance_with_extreme_volatility PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_create_validation_result_invalid PASSED [ 56%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_create_validation_result_valid PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_edge_case_all_losing_trades PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_edge_case_all_winning_trades PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_edge_case_single_trade PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_generate_recommendations_high_score PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_generate_recommendations_low_score PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_init PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_pattern_degradation_with_identical_halves PASSED [ 57%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_profit_factor_with_zero_values PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_recommendations_with_mixed_results PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_sample_size_with_high_frequency_situation PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_session_performance_with_all_sessions PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_session_performance_with_invalid_times PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_behavioral_stability_long_period PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_behavioral_stability_short_period PASSED [ 58%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_context_adjusted_performance_high_volatility PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_context_adjusted_performance_low_volatility PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_cross_situational_consistency PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_pattern_degradation_insufficient_trades PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_pattern_degradation_sufficient_trades PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_session_specific_performance_disabled PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_session_specific_performance_enabled PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_situational_pattern_empty_trades PASSED [ 59%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_situational_pattern_success PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_situational_sample_size_insufficient PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_situational_sample_size_sufficient PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_volatility_adjusted_expectations_disabled PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validate_volatility_adjusted_expectations_enabled PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validation_with_empty_market_situations PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validation_with_exception_handling PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validation_with_invalid_ohlc_data PASSED [ 60%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validation_with_missing_columns PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_validation_with_none_inputs PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_expectations_failed_high_volatility PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_expectations_high_vol_underperform PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_expectations_high_volatility_scenario PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_expectations_no_trade_volatilities PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_expectations_with_proper_calculation PASSED [ 61%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_expectations_zero_market_volatility PASSED [ 62%]
tests/test_legacy_situational_validator.py::TestSituationalValidator::test_volatility_validation_with_missing_close_column PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_additional_entry_conditions PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_additional_mt4_conditions PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_candle_position_since_open PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_stop_loss PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_stop_loss_edge_cases PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_stop_loss_percentage_formats PASSED [ 62%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_take_profit PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_take_profit_edge_cases PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_calculate_take_profit_r_multiples PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_candle_position_filter PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_check_candle_position_filter PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_check_day_of_week_filter PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_check_hour_range_filter PASSED [ 63%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_check_time_filter PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_check_timeframe_breakout PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_check_timeframe_candle_bullish_bearish_edge_cases PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_complex_entry_conditions PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_complex_llm_response_parsing PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_complex_stop_loss_calculations PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_complex_take_profit_calculations PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_complex_time_filters PASSED [ 64%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_create_python_function_functionality PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_create_python_function_with_real_data PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_cross_timeframe_conditions PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_day_of_week_filter PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_direction_conversion PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_empty_and_malformed_responses PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_engulfing_pattern_conditions PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_engulfing_patterns PASSED [ 65%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_entry_condition_timeframe_patterns PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_error_conditions_comprehensive PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_error_handling PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_error_handling_and_edge_cases PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_error_handling_comprehensive PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_evaluate_cross_timeframe_condition PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_evaluate_entry_condition_simple PASSED [ 66%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_evaluate_mt4_condition PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_evaluate_simple_condition_mt4_syntax PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_field PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_field_functionality PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_field_with_bullet_points PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_individual_rules PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_max_duration PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_max_duration_edge_cases PASSED [ 67%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_patterns_from_llm_response PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_price_entry_condition PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_extract_rule_section PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_fail_fast_on_missing_data PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_generate_mt4_ea PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_generate_mt4_ea_function PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_generate_mt4_entry_condition PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_generate_mt4_time_filter PASSED [ 68%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_generate_mt4_utility_functions PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_generate_python_functions PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_get_previous_day_high PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_get_previous_day_high_low PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_get_previous_day_high_low_with_data PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_get_previous_day_low PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_get_timeframe_candle_high PASSED [ 69%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_get_timeframe_candle_low PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_hour_range_filter PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_inside_bar_pattern_conditions PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_inside_bar_patterns PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_is_mt4_condition PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_llm_rule_parser_initialization PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_module_import PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_mt4_condition_complex_expressions PASSED [ 70%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_mt4_condition_evaluation PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_mt4_ea_code_generation PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_mt4_stop_loss_calculations PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_mt4_syntax_conditions PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_mt4_value_parsing PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_llm_response_comprehensive PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_llm_response_empty PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_llm_response_success PASSED [ 71%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_llm_rule_function PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_position_size PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_position_size_variations PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_single_rule_functionality PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_single_rule_invalid PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_parse_single_rule_valid PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_percentage_based_conditions PASSED [ 72%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_percentage_based_take_profit PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_position_size_parsing_edge_cases PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_previous_day_high_conditions PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_previous_day_high_low_conditions PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_previous_day_low_conditions PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_previous_day_methods PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_price_entry_condition_extraction PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_r_multiple_edge_cases PASSED [ 73%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_real_data_only_validation PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_rule_function_calling_conventions PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_rule_parse_error_scenarios PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_timeframe_breakout_checks PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_timeframe_candle_bullish_bearish PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_timeframe_candle_checks PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_trading_rule_creation PASSED [ 74%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_validation_errors_collection PASSED [ 75%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_validation_errors_handling PASSED [ 75%]
tests/test_llm_rule_parser.py::TestLLMRuleParser::test_validation_errors_tracking PASSED [ 75%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_is_server_running_fail_fast PASSED [ 75%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_get_available_models_fail_fast PASSED [ 75%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_test_model_loading_fake_model PASSED [ 75%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_test_model_loading_fail_paths PASSED [ 75%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_test_model_loading_success PASSED [ 75%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_fail_on_missing_real_data PASSED [ 76%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_get_working_model_fail_fast PASSED [ 76%]
tests/test_lm_studio_client.py::TestLMStudioClientJaegerCompliance::test_send_message_fail_fast PASSED [ 76%]
tests/test_lm_studio_client_main.py::test_main_branches PASSED           [ 76%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_additional_metrics_empty_trades PASSED [ 76%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_additional_metrics_error_handling PASSED [ 76%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_additional_metrics_no_losing_trades PASSED [ 76%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_additional_metrics_no_winning_trades PASSED [ 76%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_additional_metrics_with_no_trades PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_calculate_performance_grade PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_calculate_performance_grade_excellent PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_calculate_performance_grade_poor PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_comprehensive_metrics_coverage PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_division_by_zero_protection PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_error_handling_in_comprehensive_metrics PASSED [ 77%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_extract_all_backtesting_metrics PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_extract_all_backtesting_metrics_exception_handling PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_extract_all_backtesting_metrics_no_attributes PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_extract_metrics_error_handling PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_extract_metrics_exception_handling PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_extract_metrics_missing_attributes PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_generate_comprehensive_metrics_table_basic PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_generate_comprehensive_metrics_table_with_rule_id PASSED [ 78%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_generate_metrics_table_exception_handling PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_generate_metrics_table_with_missing_stats PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_get_additional_metrics PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_init PASSED  [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_max_consecutive_all_false PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_max_consecutive_all_true PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_max_consecutive_edge_cases PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_max_consecutive_empty_series PASSED [ 79%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_max_consecutive_losses PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_max_consecutive_wins PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_metrics_table_formatting PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_module_import PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_no_hardcoded_params PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_performance_grade_boundary_conditions PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_performance_grade_edge_cases PASSED [ 80%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_performance_grade_poor_metrics PASSED [ 81%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_recovery_factor_calculation PASSED [ 81%]
tests/test_metrics_generator.py::TestMetricsGenerator::test_win_loss_ratio_calculation PASSED [ 81%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_convert_entry_logic_basic_patterns PASSED [ 81%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_convert_entry_logic_unknown_pattern PASSED [ 81%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_convert_entry_logic_with_backticks PASSED [ 81%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_convert_profitable_patterns_to_mt4_empty PASSED [ 81%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_convert_profitable_patterns_to_mt4_function PASSED [ 81%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_convert_target_logic_entry_price_minus_pattern PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_converter_initialization PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_empty_ea_generation PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_failfast_on_unsupported_entry_logic PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_fallback_entry_logic_coverage PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_ea_header PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_global_variables PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_init_function PASSED [ 82%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_input_parameters PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_ontick_function PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_rule_function_long PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_rule_function_short PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_generate_utility_functions PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_get_entry_price_long PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_get_entry_price_short PASSED [ 83%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_module_import PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_multiple_rules_conversion PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_no_hardcoded_params PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_current_high PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_current_low PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_default_long PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_default_short PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_percentage_long PASSED [ 84%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_percentage_short PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_previous_high PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_stop_logic_conversion_previous_low PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_supported_entry_logic_conversion PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_conversion_default_long PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_conversion_default_short PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_conversion_percentage_long PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_conversion_percentage_short PASSED [ 85%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_conversion_risk_reward_long PASSED [ 86%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_conversion_risk_reward_short PASSED [ 86%]
tests/test_mt4_hardcoded_converter.py::TestMT4HardcodedConverter::test_target_logic_subtraction_pattern_coverage PASSED [ 86%]
tests/test_order.py::test_order_creation_and_repr PASSED                 [ 86%]
tests/test_order.py::test_order_cancel PASSED                            [ 86%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_backtester_with_different_thresholds PASSED [ 86%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_create_pattern_strategy PASSED [ 86%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_create_pattern_strategy_actual PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_create_pattern_strategy_class_creation PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_create_pattern_strategy_execution PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_actual_method PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_edge_cases PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_failure PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_insufficient_trades PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_method PASSED [ 87%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_no_results PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_none_results PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_profitable PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_success PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_evaluate_profitability_unprofitable_return PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_actual_method PASSED [ 88%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_edge_cases PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_empty_data PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_method PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_missing_values PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_no_results PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_partial_data PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_extract_key_metrics_success PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_actual_method PASSED [ 89%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_failure PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_missing_data PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_no_patterns PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_no_profitable PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_no_profitable_patterns PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_profitable PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_success PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_generate_validation_report_unprofitable PASSED [ 90%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_import PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_init_custom_params PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_init_default_params PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_logger_usage PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_no_hardcoded_params PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_run_walk_forward_validation_exception PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_run_walk_forward_validation_insufficient_data PASSED [ 91%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_run_walk_forward_validation_success PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_signal_validation_failures PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_single_pattern_strategy_methods_exist PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_strategy_class_attributes PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_strategy_next_method_execution PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_strategy_next_method_no_signal PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_strategy_next_method_short_signal PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_backtesting_patterns_function PASSED [ 92%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_backtesting_patterns_main_function PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_patterns_exception_handling PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_patterns_no_rules PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_patterns_print_statements PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_patterns_success PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_patterns_success_rate_calculation PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validate_patterns_with_profitable_pattern PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validation_with_empty_data PASSED [ 93%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validation_with_insufficient_data PASSED [ 94%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validator_initialization_with_custom_thresholds PASSED [ 94%]
tests/test_pattern_walkforward_backtester.py::TestPatternWalkforwardBacktester::test_validator_initialization_with_defaults PASSED [ 94%]
tests/test_position.py::test_position_properties_and_bool PASSED         [ 94%]
tests/test_position.py::test_position_repr_and_pl SKIPPED (Framework...) [ 94%]
tests/test_situational_prompts.py::test_fail_on_missing_real_data PASSED [ 94%]
tests/test_situational_prompts.py::test_get_core_situational_questions PASSED [ 94%]
tests/test_situational_prompts.py::test_get_tom_hougaard_examples PASSED [ 95%]
tests/test_situational_prompts.py::test_generate_discovery_prompt_success_and_regime_branches PASSED [ 95%]
tests/test_situational_prompts.py::test_generate_discovery_prompt_missing_ohlc PASSED [ 95%]
tests/test_strategy.py::test_strategy_instantiation_and_repr PASSED      [ 95%]
tests/test_strategy.py::test_strategy_equity_property PASSED             [ 95%]
tests/test_trade.py::test_trade_creation_and_properties PASSED           [ 95%]
tests/test_trade.py::test_trade_set_sl_tp PASSED                         [ 95%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_aggregate_results_no_successful_folds PASSED [ 95%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_aggregate_results_with_dataframe_equity_curves PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_aggregate_results_with_equity_curves PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_aggregate_results_with_nan_sharpe_ratios PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_aggregate_results_with_zero_trades PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_data_validation_missing_columns PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_data_validation_non_datetime_index PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_data_validation_valid_data PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_default_backtest_params PASSED [ 96%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_different_split_configurations PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_empty_data_handling PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_fold_with_trades_and_equity_curve PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_fold_without_trades_and_equity_curve PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_generate_report_performance_grades PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_generate_report_strong_performance PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_generate_report_with_output_path PASSED [ 97%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_generate_walk_forward_report PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_import PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_init_custom_params PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_init_default_params PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_insufficient_data_for_splits PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_logger_initialization PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_mixed_index_types_handling PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_no_hardcoded_params PASSED [ 98%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_run_single_fold_exception_handling PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_run_single_fold_success PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_run_single_fold_with_empty_equity_curve PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_run_single_fold_with_empty_trades PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_run_walk_forward_test_success PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_run_walk_forward_test_with_failures PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_tscv_initialization PASSED [ 99%]
tests/test_walkforward_tester.py::TestWalkForwardTester::test_warnings_suppression PASSED [100%]

=============================== warnings summary ===============================
tests/test_ai_integration_legacy_situational_prompts.py::TestSituationalAnalysisPrompts::test_generate_situational_validation_prompt_real_data
  /Users/<USER>/Jaeger/tests/test_ai_integration_legacy_situational_prompts.py:69: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.
    df['datetime'] = pd.date_range(start='2023-01-01', periods=len(df), freq='T')

tests/test_backtesting__backtest.py::TestBacktestJaegerCompliance::test_out_of_money_error_branch
  /Users/<USER>/Jaeger/tests/test_backtesting__backtest.py:180: UserWarning: Some prices are larger than initial cash value. Note that fractional trading is not supported by this class. If you want to trade Bitcoin, increase initial cash, or trade μBTC or satoshis instead (see e.g. class `backtesting.lib.FractionalBacktest`.
    bt = Backtest(self.df, BurnCashStrategy, cash=1, trade_on_close=True)

tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_plot
  /Users/<USER>/Jaeger/src/backtesting/_plotting.py:65: UserWarning: FigureCanvasAgg is non-interactive, and thus cannot be shown
    plt.show()

tests/test_backtesting__plotting.py::TestBacktestingPlottingJaeger::test_set_bokeh_output
  /Users/<USER>/Jaeger/src/backtesting/_plotting.py:14: UserWarning: Bokeh not available for plotting
    warnings.warn("Bokeh not available for plotting")

tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_barssince
  /Users/<USER>/Jaeger/src/backtesting/lib.py:73: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
    return next(compress(range(len(condition)), reversed(condition)), default)

tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_resample_apply
tests/test_backtesting_lib.py::TestBacktestingLibJaeger::test_resample_apply
  /Users/<USER>/Jaeger/src/backtesting/lib.py:290: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.
    resampled = series.resample(rule, label='right').agg(agg).dropna()

tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_add_behavioral_intelligence_error_handling
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_behavioral_intelligence_exception_recovery
tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_error_handling_invalid_datetime_index
  /Users/<USER>/Jaeger/src/behavioral_intelligence.py:194: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
    enhanced.index = pd.to_datetime(enhanced.index)

tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_behavioral_intelligence_exception_recovery
  /Users/<USER>/Jaeger/src/behavioral_intelligence.py:126: FutureWarning: The default fill_method='pad' in Series.pct_change is deprecated and will be removed in a future version. Either fill in any non-leading NA values prior to calling pct_change or specify 'fill_method=None' to not fill NA values.
    enhanced['price_change'] = enhanced['Close'].pct_change()

tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_behavioral_intelligence_exception_recovery
  /Users/<USER>/Jaeger/src/behavioral_intelligence.py:127: FutureWarning: The default fill_method='pad' in Series.pct_change is deprecated and will be removed in a future version. Either fill in any non-leading NA values prior to calling pct_change or specify 'fill_method=None' to not fill NA values.
    enhanced['momentum_3'] = enhanced['Close'].pct_change(3)

tests/test_behavioral_intelligence.py::TestBehavioralIntelligence::test_behavioral_intelligence_exception_recovery
  /Users/<USER>/Jaeger/src/behavioral_intelligence.py:128: FutureWarning: The default fill_method='pad' in Series.pct_change is deprecated and will be removed in a future version. Either fill in any non-leading NA values prior to calling pct_change or specify 'fill_method=None' to not fill NA values.
    enhanced['momentum_5'] = enhanced['Close'].pct_change(5)

tests/test_cortex.py::TestCortex::test_pattern_execution_error_handling
  /Users/<USER>/Jaeger/tests/test_cortex.py:2999: FutureWarning:
  
  'H' is deprecated and will be removed in a future version, please use 'h' instead.

tests/test_cortex.py::TestCortex::test_pattern_execution_error_handling
tests/test_cortex.py::TestCortex::test_pattern_execution_error_handling
tests/test_cortex.py::TestCortex::test_pattern_strategy_initialization
tests/test_cortex.py::TestCortex::test_pattern_strategy_next_method
tests/test_cortex.py::TestCortex::test_run_single_pattern_backtest_comprehensive
  /Users/<USER>/Jaeger/src/cortex.py:919: UserWarning:
  
  Data index is not datetime. Assuming simple periods, but `pd.DateTimeIndex` is advised.

tests/test_cortex.py::TestCortex::test_pattern_strategy_initialization
  /Users/<USER>/Jaeger/tests/test_cortex.py:1148: FutureWarning:
  
  'H' is deprecated and will be removed in a future version, please use 'h' instead.

tests/test_cortex.py::TestCortex::test_pattern_strategy_next_method
  /Users/<USER>/Jaeger/tests/test_cortex.py:1211: FutureWarning:
  
  'H' is deprecated and will be removed in a future version, please use 'h' instead.

tests/test_cortex.py::TestCortex::test_run_single_pattern_backtest_comprehensive
  /Users/<USER>/Jaeger/tests/test_cortex.py:3087: FutureWarning:
  
  'H' is deprecated and will be removed in a future version, please use 'h' instead.

tests/test_cortex.py::TestCortex::test_validate_order_parameters_direction_detection
  /Users/<USER>/Jaeger/tests/test_cortex.py:1422: FutureWarning:
  
  'H' is deprecated and will be removed in a future version, please use 'h' instead.

tests/test_cortex.py::TestCortex::test_validate_order_parameters_missing_params
  /Users/<USER>/Jaeger/tests/test_cortex.py:1374: FutureWarning:
  
  'H' is deprecated and will be removed in a future version, please use 'h' instead.

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
================================ tests coverage ================================
_______________ coverage: platform darwin, python 3.13.2-final-0 _______________

Name                                               Stmts   Miss  Cover   Missing
--------------------------------------------------------------------------------
src/__init__.py                                        0      0   100%
src/ai_integration/__init__.py                         0      0   100%
src/ai_integration/legacy_situational_prompts.py      50      0   100%
src/ai_integration/lm_studio_client.py               103     13    87%   20-22, 30-32, 80-85, 154, 180
src/ai_integration/situational_prompts.py             49      0   100%
src/backtesting/__init__.py                           16      0   100%
src/backtesting/_backtest/__init__.py                 82     12    85%   52-55, 58, 132-133, 171-176
src/backtesting/_broker.py                           201     21    90%   99, 143, 158, 192, 214-215, 259, 293-303, 319, 335, 337, 351, 355, 357, 383, 385
src/backtesting/_plotting.py                          60     16    73%   12, 84-110
src/backtesting/_stats.py                             74      2    97%   128-129
src/backtesting/_util.py                             107      3    97%   27, 61, 76
src/backtesting/backtesting.py                       273     24    91%   126-128, 133-134, 137, 148, 436-441, 630, 681, 696, 699-710
src/backtesting/lib.py                                87     18    79%   186-196, 280-283, 300-301, 309-313, 317
src/backtesting_rule_parser.py                       156      0   100%
src/behavioral_intelligence.py                       153      5    97%   213, 243, 294-296
src/chart_html_generator.py                           67      3    96%   297-299
src/config.py                                        148      1    99%   214
src/cortex.py                                        715    128    82%   205, 445-449, 452-453, 686-688, 703-725, 728-819, 823-858, 866-870, 894-895, 904-905, 976-981, 1097-1098, 1212
src/data_ingestion.py                                 84      0   100%
src/fact_checker.py                                   71      1    99%   143
src/file_generator.py                                180     11    94%   193-195, 293-295, 316-318, 434-435
src/legacy_situational_validator.py                  345     31    91%   60, 72, 84, 109-111, 133-134, 139-140, 147-149, 154-155, 234-236, 281, 366-367, 423-429, 579-580, 612-613
src/llm_rule_parser.py                               807    190    76%   65-66, 69, 107, 116, 172, 233, 269, 276, 288, 296, 308, 312, 316-319, 390, 408, 422, 436, 464-465, 468-469, 472-473, 476-477, 480-481, 487-489, 493-494, 498-499, 506, 533, 556-557, 574-575, 606, 663-664, 676-679, 684-686, 691-693, 717-719, 745, 754-764, 780-781, 790-795, 805-806, 815-820, 830-831, 853, 857, 870-871, 873-874, 891, 895, 902-907, 912-917, 932, 948-949, 962-963, 986-988, 994-996, 998-999, 1035-1036, 1042-1043, 1113-1121, 1132-1134, 1136-1138, 1148-1150, 1159-1160, 1183-1187, 1197-1198, 1203-1208, 1219-1268, 1389-1391, 1402-1405
src/metrics_generator.py                             201      5    98%   146-147, 206-207, 245
src/mt4_hardcoded_converter.py                       103      1    99%   264
src/pattern_walkforward_backtester.py                112      5    96%   128-131, 135, 139
src/walkforward_tester.py                            114      0   100%
--------------------------------------------------------------------------------
TOTAL                                               4358    490    89%
Coverage HTML written to dir htmlcov
================= 760 passed, 4 skipped, 24 warnings in 28.07s =================
