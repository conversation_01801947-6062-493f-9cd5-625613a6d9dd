#!/usr/bin/env python3
"""
🔧 PRINT STATEMENT MIGRATION TOOL

This script helps identify and migrate print statements to proper logging.
It categorizes print statements by priority and provides migration suggestions.

Usage:
    python bin/migrate_print_statements.py --analyze    # Analyze current print statements
    python bin/migrate_print_statements.py --migrate   # Interactive migration
    python bin/migrate_print_statements.py --report    # Generate migration report
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import argparse

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from logging_utils import categorize_print_statement, PRINT_PATTERNS

class PrintStatementAnalyzer:
    """Analyze and categorize print statements for migration to logging."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.print_statements = []
        self.excluded_files = {
            "__pycache__",
            ".pyc",
            "backtesting",  # Third-party library
            "test_",        # Test files can keep print statements
        }
    
    def should_exclude_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from analysis."""
        return any(excluded in str(file_path) for excluded in self.excluded_files)
    
    def find_print_statements(self) -> List[Dict]:
        """Find all print statements in the codebase."""
        print_pattern = re.compile(r'^\s*print\s*\([^)]*\)', re.MULTILINE)
        
        for py_file in self.src_dir.rglob("*.py"):
            if self.should_exclude_file(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for match in print_pattern.finditer(content):
                    line_num = content[:match.start()].count('\n') + 1
                    print_content = match.group(0).strip()
                    
                    # Extract the actual message from print statement
                    message_match = re.search(r'print\s*\(\s*["\']?([^"\']*)["\']?', print_content)
                    message = message_match.group(1) if message_match else print_content
                    
                    category = categorize_print_statement(message)
                    
                    self.print_statements.append({
                        'file': str(py_file),
                        'line': line_num,
                        'content': print_content,
                        'message': message,
                        'category': category,
                        'priority': self._get_priority(category, message)
                    })
                    
            except Exception as e:
                print(f"Error processing {py_file}: {e}")
        
        return self.print_statements
    
    def _get_priority(self, category: str, message: str) -> int:
        """Assign priority for migration (1=highest, 5=lowest)."""
        if category == "ERROR":
            return 1
        elif category == "WARNING":
            return 2
        elif any(keyword in message.upper() for keyword in ["CRITICAL", "FAIL", "EXCEPTION"]):
            return 1
        elif any(keyword in message.upper() for keyword in ["SUCCESS", "COMPLETE", "DONE"]):
            return 3
        elif category == "INFO":
            return 4
        else:
            return 5
    
    def generate_report(self) -> str:
        """Generate a migration report."""
        if not self.print_statements:
            self.find_print_statements()
        
        total = len(self.print_statements)
        by_category = {}
        by_priority = {}
        by_file = {}
        
        for stmt in self.print_statements:
            # Count by category
            cat = stmt['category']
            by_category[cat] = by_category.get(cat, 0) + 1
            
            # Count by priority
            pri = stmt['priority']
            by_priority[pri] = by_priority.get(pri, 0) + 1
            
            # Count by file
            file = stmt['file']
            by_file[file] = by_file.get(file, 0) + 1
        
        report = f"""
🔧 PRINT STATEMENT MIGRATION REPORT
=====================================

📊 SUMMARY:
Total print statements found: {total}

📈 BY CATEGORY:
"""
        for cat, count in sorted(by_category.items()):
            report += f"  {cat}: {count}\n"
        
        report += f"""
🎯 BY PRIORITY (1=highest):
"""
        for pri, count in sorted(by_priority.items()):
            report += f"  Priority {pri}: {count}\n"
        
        report += f"""
📁 TOP FILES WITH MOST PRINT STATEMENTS:
"""
        top_files = sorted(by_file.items(), key=lambda x: x[1], reverse=True)[:10]
        for file, count in top_files:
            report += f"  {file}: {count}\n"
        
        report += f"""
🚨 HIGH PRIORITY STATEMENTS (Priority 1-2):
"""
        high_priority = [s for s in self.print_statements if s['priority'] <= 2]
        for stmt in high_priority[:20]:  # Show first 20
            report += f"  {stmt['file']}:{stmt['line']} - {stmt['category']} - {stmt['message'][:60]}...\n"
        
        if len(high_priority) > 20:
            report += f"  ... and {len(high_priority) - 20} more high priority statements\n"
        
        return report
    
    def get_migration_suggestions(self, limit: int = 10) -> List[Dict]:
        """Get migration suggestions for highest priority print statements."""
        if not self.print_statements:
            self.find_print_statements()
        
        # Sort by priority and return top items
        sorted_statements = sorted(self.print_statements, key=lambda x: (x['priority'], x['file']))
        
        suggestions = []
        for stmt in sorted_statements[:limit]:
            category = stmt['category'].lower()
            
            if category == "error":
                suggestion = f"log_error(f\"{stmt['message']}\")"
            elif category == "warning":
                suggestion = f"log_warning(f\"{stmt['message']}\")"
            elif category == "success":
                suggestion = f"log_info(f\"✅ {stmt['message']}\")"
            else:
                suggestion = f"log_info(f\"{stmt['message']}\")"
            
            suggestions.append({
                **stmt,
                'suggestion': suggestion
            })
        
        return suggestions

def main():
    parser = argparse.ArgumentParser(description='Migrate print statements to logging')
    parser.add_argument('--analyze', action='store_true', help='Analyze print statements')
    parser.add_argument('--report', action='store_true', help='Generate migration report')
    parser.add_argument('--migrate', action='store_true', help='Interactive migration')
    parser.add_argument('--limit', type=int, default=10, help='Limit for suggestions')
    
    args = parser.parse_args()
    
    analyzer = PrintStatementAnalyzer()
    
    if args.analyze or args.report:
        report = analyzer.generate_report()
        print(report)
        
        if args.report:
            # Save report to file
            with open('print_migration_report.txt', 'w') as f:
                f.write(report)
            print(f"\n📁 Report saved to: print_migration_report.txt")
    
    if args.migrate:
        suggestions = analyzer.get_migration_suggestions(args.limit)
        
        print(f"\n🔧 TOP {len(suggestions)} MIGRATION SUGGESTIONS:")
        print("=" * 50)
        
        for i, suggestion in enumerate(suggestions, 1):
            print(f"\n{i}. {suggestion['file']}:{suggestion['line']}")
            print(f"   Category: {suggestion['category']} (Priority: {suggestion['priority']})")
            print(f"   Current:  {suggestion['content']}")
            print(f"   Suggest:  {suggestion['suggestion']}")
            print(f"   Message:  {suggestion['message'][:80]}...")
    
    if not any([args.analyze, args.report, args.migrate]):
        parser.print_help()

if __name__ == "__main__":
    main()
