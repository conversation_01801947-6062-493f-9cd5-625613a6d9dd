#!/bin/bash

# Jaeger LLM Pattern Discovery System Setup Script
# This script sets up the Python virtual environment and installs dependencies

echo "🤖 Setting up Jaeger LLM Pattern Discovery System..."
echo "=================================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment
echo "📦 Creating virtual environment..."
if [ -d "llm_env" ]; then
    echo "⚠️  Virtual environment already exists. Removing old one..."
    rm -rf llm_env
fi

python3 -m venv llm_env

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source llm_env/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Make scripts executable
echo "🔧 Making scripts executable..."
chmod +x bin/*.sh
chmod +x *.command 2>/dev/null || true

echo ""
echo "✅ Setup complete!"
echo ""
echo "🚀 To run the system:"
echo "   1. Double-click: run_jaeger.command"
echo "   2. Manual: source llm_env/bin/activate && python src/cortex.py"
echo ""
echo "📖 For more information, see docs/USER_DOCUMENTATION.md"
