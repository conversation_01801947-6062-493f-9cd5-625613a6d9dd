#!/bin/bash

# 🚨 CRITICAL: Verify No Fallbacks Script
# This script searches for forbidden fallback patterns in the Jaeger codebase

echo "🚨 CRITICAL: Verifying NO FALLBACKS in Jaeger codebase..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

VIOLATIONS=0

echo ""
echo "🔍 Searching for FORBIDDEN fallback patterns..."

# Search for default_ variables (but exclude legitimate config parameters)
echo ""
echo "❌ Checking for 'default_' variables..."
# Exclude legitimate patterns:
# - DEFAULT_ (uppercase config parameters)
# - default_factory (Python dataclass defaults)
# - default_model = config.* (reading from config)
# - print statements with default_model (error messages)
# - comparisons with default_model (legitimate usage)
DEFAULT_VARS=$(grep -r "default_" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc" | grep -v "default_rng" | grep -v "NO FALLBACKS" | grep -v "src/backtesting/" | grep -v "DEFAULT_" | grep -v "config\." | grep -v "os\.getenv" | grep -v "default_factory" | grep -v "default_model.*=" | grep -v "print.*default_model" | grep -v "== default_model" | grep -v "base_name == default_model")
if [ ! -z "$DEFAULT_VARS" ]; then
    echo -e "${RED}VIOLATION FOUND: default_ variables${NC}"
    echo "$DEFAULT_VARS"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ No default_ variables found${NC}"
fi

# Search for fallback logic (but exclude anti-fallback messages)
echo ""
echo "❌ Checking for 'fallback' logic..."
# Exclude anti-fallback messages that enforce the no-fallback principle
# Also exclude string checks for error messages containing 'no fallback'
FALLBACK_LOGIC=$(grep -r "fallback" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc" | grep -v "no fallbacks allowed" | grep -v "NO FALLBACKS" | grep -v "src/backtesting/" | grep -v "No fallback" | grep -v "must fail rather than use fallbacks" | grep -v "no-fallback principle" | grep -v "Zero fallback" | grep -v "ZERO FALLBACKS" | grep -v "violates.*fallback" | grep -v "FAIL HARD.*fallback" | grep -v "'no fallback'" | grep -v "\"no fallback\"")
if [ ! -z "$FALLBACK_LOGIC" ]; then
    echo -e "${RED}VIOLATION FOUND: fallback logic${NC}"
    echo "$FALLBACK_LOGIC"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ No fallback logic found${NC}"
fi

# Search for hard-coded percentages
echo ""
echo "❌ Checking for hard-coded percentages..."
HARDCODED_PCT=$(grep -r "0\.00[0-9]" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc" | grep -v "test_" | grep -v "src/backtesting/")
if [ ! -z "$HARDCODED_PCT" ]; then
    echo -e "${YELLOW}WARNING: Hard-coded percentages found (review needed)${NC}"
    echo "$HARDCODED_PCT"
    # Don't count as violation if they're in proper LLM parsing context
fi

# Search for else return default patterns
echo ""
echo "❌ Checking for 'else return default' patterns..."
ELSE_DEFAULT=$(grep -r "else.*return.*\*" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc")
if [ ! -z "$ELSE_DEFAULT" ]; then
    echo -e "${RED}VIOLATION FOUND: else return default patterns${NC}"
    echo "$ELSE_DEFAULT"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ No else return default patterns found${NC}"
fi

# Search for specific forbidden patterns
echo ""
echo "❌ Checking for specific forbidden patterns..."

# Check for 0.992 (0.8% stop loss fallback)
STOP_FALLBACK=$(grep -r "0\.992\|0\.008" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc")
if [ ! -z "$STOP_FALLBACK" ]; then
    echo -e "${RED}VIOLATION FOUND: 0.8% stop loss fallback${NC}"
    echo "$STOP_FALLBACK"
    VIOLATIONS=$((VIOLATIONS + 1))
fi

# Check for 1.012 (1.2% take profit fallback)
PROFIT_FALLBACK=$(grep -r "1\.012\|0\.988" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc")
if [ ! -z "$PROFIT_FALLBACK" ]; then
    echo -e "${RED}VIOLATION FOUND: 1.2% take profit fallback${NC}"
    echo "$PROFIT_FALLBACK"
    VIOLATIONS=$((VIOLATIONS + 1))
fi

# Check for rule_hash fallback
HASH_FALLBACK=$(grep -r "rule_hash\|hash.*rule" src/ 2>/dev/null | grep -v "# FIXED" | grep -v "docs/" | grep -v ".pyc")
if [ ! -z "$HASH_FALLBACK" ]; then
    echo -e "${RED}VIOLATION FOUND: rule hash fallback${NC}"
    echo "$HASH_FALLBACK"
    VIOLATIONS=$((VIOLATIONS + 1))
fi

echo ""
echo "=============================================="

if [ $VIOLATIONS -eq 0 ]; then
    echo -e "${GREEN}🎉 SUCCESS: NO FALLBACKS FOUND!${NC}"
    echo -e "${GREEN}✅ Jaeger system follows the NO FALLBACKS principle${NC}"
    echo -e "${GREEN}✅ System will work exactly as LLM intended or fail completely${NC}"
    exit 0
else
    echo -e "${RED}🚨 CRITICAL: $VIOLATIONS FALLBACK VIOLATIONS FOUND!${NC}"
    echo -e "${RED}❌ These must be removed immediately${NC}"
    echo -e "${RED}❌ Fallbacks cause catastrophic damage to the project${NC}"
    echo ""
    echo "📖 See docs/CRITICAL_NO_FALLBACKS_PRINCIPLE.md for details"
    exit 1
fi
