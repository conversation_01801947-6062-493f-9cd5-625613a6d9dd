============================= test session starts ==============================
platform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/Jaeger
plugins: cov-6.2.1
collected 16 items

tests/test_backtesting_backtesting.py ....s........s../Users/<USER>/J<PERSON>ger/llm_env/lib/python3.9/site-packages/coverage/inorout.py:509: CoverageWarning: Module src/backtesting/backtesting.py was never imported. (module-not-imported)
  self.warn(f"Module {pkg} was never imported.", slug="module-not-imported")
/Users/<USER>/Jaeger/llm_env/lib/python3.9/site-packages/coverage/control.py:915: CoverageWarning: No data was collected. (no-data-collected)
  self._warn("No data was collected.", slug="no-data-collected")
/Users/<USER>/Jaeger/llm_env/lib/python3.9/site-packages/pytest_cov/plugin.py:346: CovReportWarning: Failed to generate report: No data to report.

  self.cov_controller.finish()

WARNING: Failed to generate report: No data to report.

                                                                         [100%]

================================ tests coverage ================================
_______________ coverage: platform darwin, python 3.9.6-final-0 ________________

======================== 14 passed, 2 skipped in 0.84s =========================
