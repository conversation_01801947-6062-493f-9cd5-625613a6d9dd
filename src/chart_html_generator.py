#!/usr/bin/env python3
"""
HTML Chart Generator for Jaeger Trading System
Generates interactive HTML charts using Plotly from backtesting.py results
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import logging

logger = logging.getLogger(__name__)

class HTMLChartGenerator:
    """Generate interactive HTML charts from backtesting results"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_backtest_html_chart(self, stats, ohlc_data, symbol, output_path):
        """
        Generate comprehensive HTML chart from backtesting.py results
        
        Args:
            stats: backtesting.py stats object
            ohlc_data: OHLC price data DataFrame
            symbol: Trading symbol (e.g., 'EURUSD')
            output_path: Path to save HTML file
            
        Returns:
            str: Path to generated HTML file or None if failed
        """
        try:
            # Create subplots: Price chart + Equity curve + Drawdown
            fig = make_subplots(
                rows=3, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                subplot_titles=(
                    f'{symbol} - Price Chart with Trades',
                    'Equity Curve',
                    'Drawdown'
                ),
                row_heights=[0.5, 0.3, 0.2]
            )
            
            # 1. Add OHLC candlestick chart
            fig.add_trace(
                go.Candlestick(
                    x=ohlc_data.index,
                    open=ohlc_data['Open'],
                    high=ohlc_data['High'],
                    low=ohlc_data['Low'],
                    close=ohlc_data['Close'],
                    name='Price',
                    showlegend=False
                ),
                row=1, col=1
            )
            
            # 2. Add trade markers if trades exist
            if hasattr(stats, '_trades') and not stats._trades.empty:
                trades_df = stats._trades
                
                # Entry points (green triangles up)
                fig.add_trace(
                    go.Scatter(
                        x=trades_df['EntryTime'],
                        y=trades_df['EntryPrice'],
                        mode='markers',
                        marker=dict(
                            symbol='triangle-up',
                            size=10,
                            color='green',
                            line=dict(width=1, color='darkgreen')
                        ),
                        name='Entry',
                        hovertemplate='Entry: %{y:.4f}<br>Time: %{x}<extra></extra>'
                    ),
                    row=1, col=1
                )
                
                # Exit points (red triangles down)
                fig.add_trace(
                    go.Scatter(
                        x=trades_df['ExitTime'],
                        y=trades_df['ExitPrice'],
                        mode='markers',
                        marker=dict(
                            symbol='triangle-down',
                            size=10,
                            color='red',
                            line=dict(width=1, color='darkred')
                        ),
                        name='Exit',
                        hovertemplate='Exit: %{y:.4f}<br>Time: %{x}<extra></extra>'
                    ),
                    row=1, col=1
                )
            else:
                # CRITICAL FIX: Add annotation for 0 trades case
                fig.add_annotation(
                    text="No trades executed",
                    xref="x", yref="y",
                    x=ohlc_data.index[len(ohlc_data)//2],  # Middle of time range
                    y=ohlc_data['Close'].mean(),  # Middle of price range
                    showarrow=True,
                    arrowhead=2,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor="red",
                    font=dict(size=16, color="red"),
                    bgcolor="rgba(255,255,255,0.9)",
                    bordercolor="red",
                    borderwidth=2,
                    row=1, col=1
                )

            # 3. Add equity curve
            if hasattr(stats, '_equity_curve') and not stats._equity_curve.empty:
                equity_data = stats._equity_curve

                fig.add_trace(
                    go.Scatter(
                        x=equity_data.index,
                        y=equity_data['Equity'],
                        mode='lines',
                        line=dict(color='blue', width=2),
                        name='Equity',
                        hovertemplate='Equity: %{y:.2f}<br>Time: %{x}<extra></extra>'
                    ),
                    row=2, col=1
                )

                # Calculate drawdown for display
                initial_equity = equity_data['Equity'].iloc[0]
                peak_equity = equity_data['Equity'].expanding().max()
                drawdown_pct = ((equity_data['Equity'] - peak_equity) / peak_equity * 100)

                fig.add_trace(
                    go.Scatter(
                        x=equity_data.index,
                        y=drawdown_pct,
                        mode='lines',
                        fill='tonexty',
                        line=dict(color='red', width=1),
                        fillcolor='rgba(255,0,0,0.3)',
                        name='Drawdown %',
                        hovertemplate='Drawdown: %{y:.2f}%<br>Time: %{x}<extra></extra>'
                    ),
                    row=3, col=1
                )
            else:
                # CRITICAL FIX: Create flat equity line for 0 trades case
                initial_cash = stats.get('Equity Final [$]', 100000)
                flat_equity = [initial_cash] * len(ohlc_data)

                fig.add_trace(
                    go.Scatter(
                        x=ohlc_data.index,
                        y=flat_equity,
                        mode='lines',
                        line=dict(color='blue', width=2),
                        name='Equity (No Trades)',
                        hovertemplate='Equity: %{y:.2f}<br>Time: %{x}<extra></extra>'
                    ),
                    row=2, col=1
                )

                # Flat drawdown line at 0%
                fig.add_trace(
                    go.Scatter(
                        x=ohlc_data.index,
                        y=[0] * len(ohlc_data),
                        mode='lines',
                        line=dict(color='green', width=1),
                        name='Drawdown (0%)',
                        hovertemplate='Drawdown: 0.00%<br>Time: %{x}<extra></extra>'
                    ),
                    row=3, col=1
                )
            
            # Update layout
            fig.update_layout(
                title=f'{symbol} - Backtesting Results',
                height=800,
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                template='plotly_white'
            )
            
            # Update x-axes
            fig.update_xaxes(title_text="Time", row=3, col=1)
            
            # Update y-axes
            fig.update_yaxes(title_text="Price", row=1, col=1)
            fig.update_yaxes(title_text="Equity", row=2, col=1)
            fig.update_yaxes(title_text="Drawdown %", row=3, col=1)
            
            # Add performance metrics as annotation
            metrics_text = self._format_performance_metrics(stats)
            fig.add_annotation(
                text=metrics_text,
                xref="paper", yref="paper",
                x=0.02, y=0.98,
                showarrow=False,
                align="left",
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="black",
                borderwidth=1,
                font=dict(size=10, family="monospace")
            )
            
            # Save HTML file
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            fig.write_html(output_path)
            
            self.logger.info(f"✅ HTML chart generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"❌ Error generating HTML chart: {e}")
            return None
    
    def _format_performance_metrics(self, stats):
        """Format key performance metrics for display"""
        try:
            metrics = [
                f"Return: {stats.get('Return [%]', 0):.2f}%",
                f"Trades: {stats.get('# Trades', 0)}",
                f"Win Rate: {stats.get('Win Rate [%]', 0):.1f}%",
                f"Profit Factor: {stats.get('Profit Factor', 0):.2f}",
                f"Max Drawdown: {stats.get('Max. Drawdown [%]', 0):.2f}%",
                f"Sharpe Ratio: {stats.get('Sharpe Ratio', 0):.2f}",
                f"Start: {stats.get('Start', 'N/A')}",
                f"End: {stats.get('End', 'N/A')}"
            ]
            return "<br>".join(metrics)
        except Exception as e:
            return f"Metrics unavailable: {e}"
    
    def generate_optimization_heatmap(self, optimization_results, output_path):
        """
        Generate HTML heatmap for optimization results
        
        Args:
            optimization_results: Results from bt.optimize()
            output_path: Path to save HTML file
            
        Returns:
            str: Path to generated HTML file or None if failed
        """
        try:
            if optimization_results is None or optimization_results.empty:
                self.logger.warning("No optimization results to plot")
                return None
            
            # Create heatmap figure
            fig = go.Figure()
            
            # Convert optimization results to heatmap format
            # This would need to be customized based on the actual optimization parameters
            # For now, create a placeholder
            
            fig.add_trace(
                go.Heatmap(
                    z=[[1, 2, 3], [4, 5, 6], [7, 8, 9]],  # Placeholder data
                    colorscale='Viridis',
                    showscale=True
                )
            )
            
            fig.update_layout(
                title='Parameter Optimization Heatmap',
                xaxis_title='Parameter 1',
                yaxis_title='Parameter 2',
                template='plotly_white'
            )
            
            # Save HTML file
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            fig.write_html(output_path)
            
            self.logger.info(f"✅ Optimization heatmap generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"❌ Error generating optimization heatmap: {e}")
            return None

# REMOVED: Wrapper function - Cortex will use HTMLChartGenerator() directly
