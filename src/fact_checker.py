#!/usr/bin/env python3
"""
LLM Response Fact Checker
Validates LLM claims against actual data to prevent fabrication
"""

import pandas as pd
import re

class LLMFactChecker:
    """
    Validates LLM responses against actual data to prevent fabrication
    """
    
    def __init__(self, data):
        self.data = data
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_response(self, llm_response):
        """Main validation method"""
        print("🔍 Fact-checking LLM response...")
        
        # Reset validation state for fresh validation
        self.validation_errors = []
        self.validation_warnings = []
        
        # Check volume claims
        self._check_volume_claims(llm_response)
        
        # Check time-based claims
        self._check_time_claims(llm_response)
        
        # Check fabricated metrics
        self._check_fabricated_metrics(llm_response)
        
        # Generate validation report
        return self._generate_validation_report(llm_response)
    
    def _check_volume_claims(self, response):
        """Validate volume-related claims"""
        if 'volume' not in self.data.columns:
            return  # Skip volume checks if not present
        actual_avg_volume = self.data['volume'].mean()
        # Extract volume numbers from response
        volume_matches = re.findall(r'volume[^\d]*(\d+(?:,\d+)*)', response.lower())
        volume_tolerance = 0.5  # 50% tolerance for volume claims
        for match in volume_matches:
            claimed_volume = int(match.replace(',', ''))
            # Check if claimed volume is reasonable
            if abs(claimed_volume - actual_avg_volume) > actual_avg_volume * volume_tolerance:
                self.validation_errors.append(
                    f"Volume claim error: LLM claimed {claimed_volume:,}, actual average is {actual_avg_volume:,.0f}"
                )
    
    def _check_time_claims(self, response):
        """Validate time-based pattern claims"""
        # Check if LLM mentions specific hours
        hour_matches = re.findall(r'(\d{1,2}):00', response)
        
        for hour_str in hour_matches:
            hour = int(hour_str)
            if hour not in self.data['hour'].unique():
                self.validation_warnings.append(
                    f"Time claim warning: LLM mentioned {hour}:00 but no data exists for this hour"
                )
    
    def _check_fabricated_metrics(self, response):
        """Check for completely fabricated metrics"""
        # Only flag obviously fabricated specific numbers, not general pattern descriptions
        fabrication_patterns = [
            (r'(\d+\.?\d*)%\s*(?:success|win)\s*rate', "specific win/success rates"),
            (r'(?:win\s*rate\s*is\s*)?(\d+\.?\d*)%', "specific win/success rates"),
            (r'(\d+\.?\d*)r', "specific R-multiple returns"),
            (r'exactly\s+(\d+)\s*times?\s*per\s*(?:month|day)', "exact frequency claims")
        ]

        for pattern, metric_type in fabrication_patterns:
            matches = re.findall(pattern, response.lower())
            if matches:
                self.validation_warnings.append(
                    f"Potentially fabricated metric: LLM provided {metric_type} - verify against backtest results"
                )
    
    def _generate_validation_report(self, original_response):
        """Generate validated response with warnings"""
        
        if not self.validation_errors and not self.validation_warnings:
            return original_response + "\n\n✅ **VALIDATION PASSED**: All claims verified against actual data."
        
        validation_section = "\n\n## 🚨 FACT-CHECK RESULTS\n\n"
        
        if self.validation_errors:
            validation_section += "### ❌ ERRORS (Fabricated Data):\n"
            for error in self.validation_errors:
                validation_section += f"- {error}\n"
            validation_section += "\n"
        
        if self.validation_warnings:
            validation_section += "### ⚠️ WARNINGS (Verify Claims):\n"
            for warning in self.validation_warnings:
                validation_section += f"- {warning}\n"
            validation_section += "\n"
        
        validation_section += "**RECOMMENDATION**: Only trust observable patterns in the data. Ignore fabricated performance metrics.\n"
        
        return original_response + validation_section

def main():
    """Test fact checker"""
    # Example usage
    test_response = """
    Pattern 1: Morning Volume Surge
    - 70% success rate with 3x volume
    - Average volume: 150,000 (actual: 59,241)
    - 12 times per month frequency
    - 2.5R average profit
    """
    
    # Load test data from real test data directory
    import os
    test_data_file = os.path.join('tests', 'RealTestData', 'dax_200_bars.csv')
    if os.path.exists(test_data_file):
        data = pd.read_csv(test_data_file)
        # Convert to proper datetime format for fact checking
        data['datetime'] = pd.to_datetime(data['Date'].astype(str) + ' ' + data['Time'].astype(str))
        data = data.rename(columns={
            'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'
        })

        checker = LLMFactChecker(data)
        validated_response = checker.validate_response(test_response)

        print("=== ORIGINAL RESPONSE ===")
        print(test_response)
        print("\n=== VALIDATED RESPONSE ===")
        print(validated_response)
    else:
        print(f"Test data file not found: {test_data_file}")
        print("Please ensure real test data is available for fact checker testing.")

if __name__ == "__main__":
    main()
