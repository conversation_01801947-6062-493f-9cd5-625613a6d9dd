{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ORB Trading Pattern Schema", "description": "Schema for LLM to output Opening Range Breakout patterns ONLY - compatible with backtesting.py", "type": "object", "required": ["pattern_name", "entry_conditions", "exit_conditions"], "properties": {"pattern_name": {"type": "string", "description": "Descriptive name of the ORB pattern", "examples": ["30-Minute ORB Breakout", "London Session ORB", "Multi-Timeframe ORB Confirmation"]}, "description": {"type": "string", "description": "Detailed description of the ORB pattern and why it works"}, "market_situation": {"type": "string", "description": "Specific market conditions where this ORB pattern occurs (session, volatility, etc.)"}, "entry_conditions": {"type": "array", "description": "ORB-specific entry conditions", "minItems": 1, "items": {"type": "object", "required": ["condition"], "properties": {"condition": {"type": "string", "enum": ["orb_breakout_above", "orb_breakout_below", "opening_range_high", "opening_range_low", "close_above_orb_high", "close_below_orb_low", "candles_since_session_start", "session_filter", "orb_range_size", "orb_time_filter", "multi_timeframe_orb_confirm"]}, "orb_period_minutes": {"type": "integer", "description": "Opening range period in minutes (e.g., 30, 60, 90)", "minimum": 15, "maximum": 240}, "session": {"type": "string", "enum": ["london", "ny", "asian", "london_ny_overlap", "all"], "description": "Trading session for ORB calculation - must match backtester session values"}, "start_hour": {"type": "integer", "description": "Session start hour (24-hour format)", "minimum": 0, "maximum": 23}, "end_hour": {"type": "integer", "description": "Session end hour (24-hour format)", "minimum": 0, "maximum": 23}, "direction": {"type": "string", "enum": ["long", "short", "both"], "description": "ORB breakout direction"}}, "additionalProperties": true}}, "entry_logic": {"type": "string", "enum": ["AND", "OR"], "default": "AND", "description": "Logic operator for combining entry conditions"}, "exit_conditions": {"type": "array", "description": "ORB-specific exit conditions", "minItems": 1, "items": {"type": "object", "required": ["condition"], "properties": {"condition": {"type": "string", "enum": ["trailing_stop_candle_low", "trailing_stop_candle_high", "fixed_pips_stop", "fixed_pips_target", "percentage_stop", "percentage_target", "orb_pattern_failure", "session_end_exit", "trailing_high_low", "support_resistance_exit", "time_based_exit", "profit_target_pips", "candle_close_opposite", "break_of_structure", "return_to_orb_range", "session_transition_exit", "trail_previous_candle_low", "trail_previous_candle_high", "orb_range_reentry", "new_session_exit"], "description": "SOPHISTICATED MARKET-STRUCTURE EXITS ONLY - NO risk/reward ratios allowed"}, "pips": {"type": "number", "description": "Pip distance for fixed pip-based exits", "minimum": 5, "maximum": 200}, "percentage": {"type": "number", "description": "Percentage for percentage-based exits (e.g., 0.5 = 0.5%)", "minimum": 0.1, "maximum": 5.0}, "lookback_candles": {"type": "integer", "description": "Number of candles to look back for trailing stops", "minimum": 1, "maximum": 10}, "trail_distance_pips": {"type": "number", "description": "Distance in pips for trailing stop", "minimum": 5, "maximum": 50}, "activation_profit_pips": {"type": "number", "description": "Profit in pips before trailing stop activates", "minimum": 10, "maximum": 100}, "session": {"type": "string", "enum": ["london", "ny", "asian", "london_ny_overlap"], "description": "Session for session-based exits"}, "minutes_before_session_end": {"type": "integer", "description": "Minutes before session end to exit", "minimum": 5, "maximum": 120}, "candle_count": {"type": "integer", "description": "Number of candles for pattern-based exits", "minimum": 1, "maximum": 5}, "trigger": {"type": "string", "enum": ["close_back_inside_range", "break_orb_opposite", "new_session_candle", "volume_spike"], "description": "Specific trigger for pattern failure exits"}}, "additionalProperties": true}}, "filters": {"type": "array", "description": "Additional filters that must be met (market regime, time, etc.)", "items": {"type": "object", "required": ["condition"], "properties": {"condition": {"type": "string", "enum": ["low_volatility_regime", "high_volatility_regime", "trend_filter", "volume_filter", "time_filter", "day_filter", "session_filter"]}, "lookback": {"type": "integer", "minimum": 1, "maximum": 252}, "threshold": {"type": "number", "minimum": 0, "maximum": 1}, "value": {"type": ["string", "number"], "description": "Filter value (day name, hour, etc.)"}}, "additionalProperties": true}}, "position_sizing": {"type": "object", "description": "Position sizing methodology", "properties": {"method": {"type": "string", "enum": ["fixed_percent", "fixed_amount", "volatility_based", "kelly_criterion"], "default": "fixed_percent"}, "value": {"type": "number", "description": "Size value (percentage for fixed_percent, dollar amount for fixed_amount)", "minimum": 0.001, "maximum": 1.0}, "max_risk": {"type": "number", "description": "Maximum risk per trade as percentage of account", "minimum": 0.001, "maximum": 0.1}}}, "statistical_edge": {"type": "object", "description": "Statistical basis for the pattern's edge", "properties": {"win_rate": {"type": "number", "description": "Expected win rate as decimal", "minimum": 0.1, "maximum": 1.0}, "avg_risk_reward": {"type": "number", "description": "Average risk-reward ratio", "minimum": 0.1, "maximum": 10.0}, "sample_size": {"type": "integer", "description": "Number of historical occurrences analyzed", "minimum": 20}, "confidence_level": {"type": "number", "description": "Statistical confidence level", "minimum": 0.5, "maximum": 0.99}}}, "optimal_conditions": {"type": "object", "description": "Optimal market conditions for pattern effectiveness", "properties": {"market_regime": {"type": "array", "items": {"type": "string", "enum": ["trending", "ranging", "volatile", "quiet", "bullish", "bearish"]}}, "timeframes": {"type": "array", "items": {"type": "string", "enum": ["1min", "5min", "10min", "15min", "30min", "60min"]}}, "sessions": {"type": "array", "items": {"type": "string", "enum": ["asian", "london", "ny", "london_ny_overlap", "all"]}}}}, "orb_logic": {"type": "string", "description": "Explanation of why this specific ORB pattern works (market psychology, institutional behavior, etc.)"}, "implementation_notes": {"type": "string", "description": "Additional notes for proper implementation and discretionary judgment"}}}