#!/usr/bin/env python3
"""
Hard-Coded MT4 Converter

Deterministic conversion from validated backtesting rules to MT4 EA code.
No LLM dependency - reliable, predictable conversion.
"""

from typing import List
from backtesting_rule_parser import TradingPattern
from config import config
import re

class HardcodedMT4Converter:
    """Converts validated backtesting rules to MT4 EA code deterministically"""
    
    def __init__(self):
        self.conversion_errors = []
    
    def convert_rules_to_mt4(self, profitable_rules: List[TradingPattern],
                           ea_name: str = "<PERSON><PERSON>ger_Validated_EA") -> str:
        """
        Convert validated backtesting rules to complete MT4 EA
        
        Args:
            profitable_rules: List of validated profitable rules
            ea_name: Name for the MT4 EA
            
        Returns:
            Complete MT4 EA code string
        """
        if not profitable_rules:
            return self._generate_empty_ea(ea_name)
        
        print(f"🔧 Converting {len(profitable_rules)} validated patterns to MT4...")
        
        # Generate MT4 EA structure
        mt4_code = self._generate_ea_header(ea_name, len(profitable_rules))
        mt4_code += self._generate_input_parameters(profitable_rules)
        mt4_code += self._generate_global_variables()
        mt4_code += self._generate_init_function()
        mt4_code += self._generate_ontick_function(profitable_rules)
        
        # Generate individual rule functions
        for rule in profitable_rules:
            mt4_code += self._generate_rule_function(rule)
        
        mt4_code += self._generate_utility_functions()
        
        print(f"✅ MT4 EA generated: {len(mt4_code)} characters")
        return mt4_code
    
    def _generate_ea_header(self, ea_name: str, num_patterns: int) -> str:
        """Generate MT4 EA header with proper alignment"""

        # Clean naming: GipsyDanger_DAX_001.mq4 should fit nicely
        filename = f"{ea_name}.mq4"
        box_width = 66

        # Center the filename in the comment box
        if len(filename) <= box_width:
            padding = box_width - len(filename)
            left_padding = padding // 2
            right_padding = padding - left_padding
            filename_line = f"//|{' ' * left_padding}{filename}{' ' * right_padding}|"
        else:
            # For very long names, left-align with minimal padding
            filename_line = f"//| {filename[:box_width-2]} |"

        return f"""//+------------------------------------------------------------------+
{filename_line}
//|                        Generated from Validated Backtesting Patterns |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Jaeger Trading System"
#property link      ""
#property version   "1.00"
#property strict

//--- EA Information
// This EA contains {num_patterns} validated patterns that passed walk-forward testing
// Each pattern has been proven profitable through rigorous backtesting

"""
    
    def _generate_input_parameters(self, rules: List[TradingPattern]) -> str:
        """Generate input parameters section"""
        params = f"""//+------------------------------------------------------------------+
//| Input Parameters
//+------------------------------------------------------------------+
input double LotSize = {config.MT4_DEFAULT_LOT_SIZE};              // Position size
input int MagicNumber = {config.MT4_MAGIC_NUMBER};           // Magic number for orders
input bool UseTimeFilter = {str(config.MT4_DEFAULT_USE_TIME_FILTER).lower()};        // Enable time filtering
input int StartHour = {config.MT4_DEFAULT_START_HOUR};                 // Trading start hour
input int EndHour = {config.MT4_DEFAULT_END_HOUR};                  // Trading end hour

// Pattern Enable/Disable Controls
"""
        
        for rule in rules:
            params += f"input bool EnablePattern{rule.rule_id} = true;  // Enable {rule.name}\n"
        
        params += "\n"
        return params
    
    def _generate_global_variables(self) -> str:
        """Generate global variables section with ORB support"""
        return """//+------------------------------------------------------------------+
//| Global Variables
//+------------------------------------------------------------------+
int totalOrders = 0;
datetime lastBarTime = 0;

// ORB-specific variables
double ORB_High = 0.0;
double ORB_Low = 0.0;
datetime ORB_StartTime = 0;
int ORB_PeriodMinutes = 30;
bool ORB_Calculated = false;

"""
    
    def _generate_init_function(self) -> str:
        """Generate OnInit function"""
        return """//+------------------------------------------------------------------+
//| Expert initialization function
//+------------------------------------------------------------------+
int OnInit()
{
   Print("Jaeger Validated EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Jaeger Validated EA deinitialized");
}

"""
    
    def _generate_ontick_function(self, rules: List[TradingPattern]) -> str:
        """Generate OnTick function with rule calls"""
        ontick = """//+------------------------------------------------------------------+
//| Expert tick function
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new bar
   if(Time[0] == lastBarTime)
      return;
   lastBarTime = Time[0];
   
   // Apply time filter if enabled
   if(UseTimeFilter && !IsTimeToTrade())
      return;
   
   // Check each validated pattern
"""
        
        for rule in rules:
            ontick += f"   if(EnablePattern{rule.rule_id}) CheckPattern{rule.rule_id}();\n"
        
        ontick += "}\n\n"
        return ontick
    
    def _generate_rule_function(self, rule: TradingPattern) -> str:
        """Generate MT4 function for a single validated rule"""
        
        # Convert entry logic
        entry_condition = self._convert_entry_logic(rule.entry_logic_text)

        # Convert stop and target logic
        stop_calculation = self._convert_stop_logic(rule.stop_logic_text, rule.direction)
        target_calculation = self._convert_target_logic(rule.target_logic_text, rule.direction)
        
        # Determine order type
        order_type = "OP_BUY" if rule.direction == 'long' else "OP_SELL"
        
        function_code = f"""//+------------------------------------------------------------------+
//| Pattern {rule.rule_id}: {rule.name}
//| Validated through walk-forward testing
//+------------------------------------------------------------------+
void CheckPattern{rule.rule_id}()
{{
   // Entry condition: {rule.entry_logic_text}
   if(!({entry_condition}))
      return;
   
   double entryPrice = {self._get_entry_price(rule.direction)};
   double stopLoss = {stop_calculation};
   double takeProfit = {target_calculation};
   
   // Validate order parameters
   if(!ValidateOrderParams(entryPrice, stopLoss, takeProfit, {order_type}))
      return;
   
   // Calculate position size
   double positionSize = CalculatePositionSize(entryPrice, stopLoss);
   
   // Place order
   int ticket = OrderSend(Symbol(), {order_type}, positionSize, entryPrice, {config.MT4_SLIPPAGE}, 
                         stopLoss, takeProfit, "Pattern{rule.rule_id}", MagicNumber, 0, clrBlue);
   
   if(ticket > 0)
   {{
      totalOrders++;
      Print("Pattern{rule.rule_id} order placed: ", ticket);
   }}
   else
   {{
      Print("Pattern{rule.rule_id} order failed: ", GetLastError());
   }}
}}

"""
        return function_code
    
    def _convert_entry_logic(self, entry_logic: str) -> str:
        """Convert backtesting entry logic to MT4 syntax"""
        # Clean the logic - remove backticks and normalize
        logic = entry_logic.lower().strip().replace('`', '')

        # Map ORB patterns and common patterns
        conversions = {
            # ORB-specific patterns
            'orb_breakout_above': 'Close[0] > ORB_High',
            'orb_breakout_below': 'Close[0] < ORB_Low',
            'close_above_orb_high': 'Close[0] > ORB_High',
            'close_below_orb_low': 'Close[0] < ORB_Low',
            'opening_range_high': 'ORB_High > 0',
            'opening_range_low': 'ORB_Low > 0',

            # Legacy patterns (for backward compatibility)
            'current_close > previous_high': 'Close[0] > High[1]',
            'current_close < previous_low': 'Close[0] < Low[1]',
            'current_close > previous_close': 'Close[0] > Close[1]',
            'current_close < previous_close': 'Close[0] < Close[1]',
            'current_high > previous_high': 'High[0] > High[1]',
            'current_low < previous_low': 'Low[0] < Low[1]'
        }

        for pattern, mt4_code in conversions.items():
            if pattern in logic:
                return mt4_code

        raise RuntimeError("UNBREAKABLE RULE VIOLATION: No fallback allowed. MT4 conversion must be deterministic.")
    
    def _convert_stop_logic(self, stop_logic: str, direction: str) -> str:
        """Convert backtesting stop logic to MT4 calculation"""
        # Clean the logic - remove backticks and normalize
        logic = stop_logic.lower().strip().replace('`', '')

        # Simple stop logic conversion (no padding needed for market orders)
        if 'previous_low' in logic:
            return 'Low[1]'
        elif 'previous_high' in logic:
            return 'High[1]'
        elif 'current_low' in logic:
            return 'Low[0]'
        elif 'current_high' in logic:
            return 'High[0]'

        # Check for percentage-based stops
        pct_match = re.search(r'(\d+\.?\d*)%', logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return f'entryPrice * {1 - pct:.6f}'
            else:
                return f'entryPrice * {1 + pct:.6f}'

        # Default stops (simple and clean)
        if direction == 'long':
            return 'Low[1]'  # Previous low for long
        else:
            return 'High[1]'  # Previous high for short
    
    def _convert_target_logic(self, target_logic: str, direction: str) -> str:
        """Convert backtesting target logic to MT4 calculation"""
        # Clean the logic - remove backticks and normalize
        logic = target_logic.lower().strip().replace('`', '')
        
        # Risk-reward based targets
        if 'entry_price + (entry_price - stop_price)' in logic:
            # Extract multiplier
            mult_match = re.search(r'\*\s*(\d+\.?\d*)', logic)
            multiplier = float(mult_match.group(1)) if mult_match else 1.0
            
            if direction == 'long':
                return f'entryPrice + (entryPrice - stopLoss) * {multiplier:.1f}'
            else:
                return f'entryPrice - (stopLoss - entryPrice) * {multiplier:.1f}'
        
        elif 'entry_price - (stop_price - entry_price)' in logic:
            # Extract multiplier
            mult_match = re.search(r'\*\s*(\d+\.?\d*)', logic)
            multiplier = float(mult_match.group(1)) if mult_match else 1.0
            
            return f'entryPrice - (stopLoss - entryPrice) * {multiplier:.1f}'
        
        # Percentage-based targets
        pct_match = re.search(r'(\d+\.?\d*)%', logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return f'entryPrice * {1 + pct:.6f}'
            else:
                return f'entryPrice * {1 - pct:.6f}'
        
        # Default 2:1 risk-reward
        if direction == 'long':
            return 'entryPrice + (entryPrice - stopLoss) * 2.0'
        else:
            return 'entryPrice - (stopLoss - entryPrice) * 2.0'
    
    def _get_entry_price(self, direction: str) -> str:
        """Get entry price for direction"""
        if direction == 'long':
            return 'Ask'
        else:
            return 'Bid'
    
    def _generate_utility_functions(self) -> str:
        """Generate utility functions"""
        return """//+------------------------------------------------------------------+
//| Utility Functions
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   int currentHour = Hour();
   return (currentHour >= StartHour && currentHour <= EndHour);
}

bool ValidateOrderParams(double entry, double sl, double tp, int orderType)
{
   // Simple validation - only check direction, no artificial distance limits
   if(orderType == OP_BUY)
   {
      return (sl < entry && tp > entry);
   }
   else if(orderType == OP_SELL)
   {
      return (sl > entry && tp < entry);
   }
   return false;
}

double CalculatePositionSize(double entry, double sl)
{
   double riskAmount = MathAbs(entry - sl);
   if(riskAmount == 0) return LotSize;

   // Simple fixed lot size for now
   return LotSize;
}

//+------------------------------------------------------------------+
//| ORB Calculation Functions
//+------------------------------------------------------------------+
void CalculateORB()
{
   if(ORB_Calculated) return;

   // Calculate opening range for current day
   datetime dayStart = iTime(Symbol(), PERIOD_D1, 0);
   int startBar = iBarShift(Symbol(), Period(), dayStart);

   if(startBar < 0) return;

   // Calculate number of bars for ORB period
   int orbBars = ORB_PeriodMinutes / Period();
   if(orbBars <= 0) orbBars = 1;

   // Find high and low of opening range
   ORB_High = High[startBar];
   ORB_Low = Low[startBar];

   for(int i = startBar; i >= startBar - orbBars && i >= 0; i--)
   {
      if(High[i] > ORB_High) ORB_High = High[i];
      if(Low[i] < ORB_Low) ORB_Low = Low[i];
   }

   ORB_Calculated = true;
   ORB_StartTime = TimeCurrent();
}

void ResetORBDaily()
{
   // Reset ORB calculation for new day
   if(TimeDay(TimeCurrent()) != TimeDay(ORB_StartTime))
   {
      ORB_Calculated = false;
      ORB_High = 0.0;
      ORB_Low = 0.0;
   }
}
"""
    
    def _generate_empty_ea(self, ea_name: str) -> str:
        """Generate empty EA when no profitable patterns"""
        return f"""//+------------------------------------------------------------------+
//|                                                {ea_name}.mq4 |
//|                        No Profitable Patterns Found |
//+------------------------------------------------------------------+
#property copyright "Jaeger Trading System"
#property version   "1.00"
#property strict

//--- No patterns passed walk-forward validation
//--- This EA will not place any trades

int OnInit()
{{
   Print("No profitable patterns found - EA will not trade");
   return(INIT_SUCCEEDED);
}}

void OnTick()
{{
   // No trading logic - no patterns passed validation
}}
"""

# Main conversion function
def convert_profitable_patterns_to_mt4(profitable_rules: List[TradingPattern],
                                     ea_name: str = "Jaeger_Validated_EA") -> str:
    """
    Convert validated profitable patterns to MT4 EA
    
    Args:
        profitable_rules: List of patterns that passed walk-forward validation
        ea_name: Name for the generated EA
        
    Returns:
        Complete MT4 EA code
    """
    converter = HardcodedMT4Converter()
    return converter.convert_rules_to_mt4(profitable_rules, ea_name)
