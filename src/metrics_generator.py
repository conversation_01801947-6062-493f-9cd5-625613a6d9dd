#!/usr/bin/env python3
"""
Metrics Generator for Jaeger Trading System
Replaces all manual calculations with backtesting.py statistics
"""

from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class MetricsGenerator:
    """
    Generate trading metrics using only backtesting.py statistics
    
    This class eliminates all manual calculations and uses the battle-tested
    statistics from the backtesting.py library to ensure accuracy and consistency.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_comprehensive_metrics_table(self, stats, rule_id: int = None) -> str:
        """
        Generate comprehensive metrics table from backtesting.py stats
        
        Args:
            stats: backtesting.py stats object
            rule_id: Optional rule identifier
            
        Returns:
            str: Formatted markdown table with all metrics
        """
        try:
            # Extract all available metrics from backtesting.py
            metrics = self._extract_all_backtesting_metrics(stats)
            
            # Generate comprehensive table
            table_lines = []
            table_lines.append("## 📊 Trading Metrics")
            if rule_id:
                table_lines.append(f"### Rule {rule_id} Performance Analysis")
            table_lines.append("")
            
            # Core Performance Metrics
            table_lines.append("### 🎯 Core Performance")
            table_lines.append("| Metric | Value | Description |")
            table_lines.append("|--------|-------|-------------|")
            table_lines.append(f"| **Total Return** | {metrics.get('Return [%]', 0):.2f}% | Overall strategy return |")
            table_lines.append(f"| **CAGR** | {metrics.get('CAGR [%]', 0):.2f}% | Compound Annual Growth Rate |")
            table_lines.append(f"| **Volatility** | {metrics.get('Volatility [%]', 0):.2f}% | Annualized volatility |")
            table_lines.append(f"| **Sharpe Ratio** | {metrics.get('Sharpe Ratio', 0):.2f} | Risk-adjusted return |")
            table_lines.append(f"| **Sortino Ratio** | {metrics.get('Sortino Ratio', 0):.2f} | Downside risk-adjusted return |")
            table_lines.append(f"| **Calmar Ratio** | {metrics.get('Calmar Ratio', 0):.2f} | Return vs max drawdown |")
            table_lines.append("")
            
            # Trading Activity Metrics
            table_lines.append("### 📈 Trading Activity")
            table_lines.append("| Metric | Value | Description |")
            table_lines.append("|--------|-------|-------------|")
            table_lines.append(f"| **Total Trades** | {metrics.get('# Trades', 0)} | Number of completed trades |")
            table_lines.append(f"| **Win Rate** | {metrics.get('Win Rate [%]', 0):.1f}% | Percentage of winning trades |")
            table_lines.append(f"| **Profit Factor** | {metrics.get('Profit Factor', 0):.2f} | Gross profit / Gross loss |")
            table_lines.append(f"| **Best Trade** | {metrics.get('Best Trade [%]', 0):.2f}% | Largest winning trade |")
            table_lines.append(f"| **Worst Trade** | {metrics.get('Worst Trade [%]', 0):.2f}% | Largest losing trade |")
            table_lines.append(f"| **Avg Trade** | {metrics.get('Avg. Trade [%]', 0):.2f}% | Average trade return |")
            table_lines.append("")
            
            # Risk Metrics
            table_lines.append("### ⚠️ Risk Analysis")
            table_lines.append("| Metric | Value | Description |")
            table_lines.append("|--------|-------|-------------|")
            table_lines.append(f"| **Max Drawdown** | {metrics.get('Max. Drawdown [%]', 0):.2f}% | Maximum peak-to-trough decline |")
            table_lines.append(f"| **Avg Drawdown** | {metrics.get('Avg. Drawdown [%]', 0):.2f}% | Average drawdown period |")
            table_lines.append(f"| **Max DD Duration** | {metrics.get('Max. Drawdown Duration', 0)} | Longest drawdown period |")
            table_lines.append(f"| **Avg DD Duration** | {metrics.get('Avg. Drawdown Duration', 0)} | Average drawdown duration |")
            table_lines.append("")
            
            # Time-Based Metrics
            table_lines.append("### ⏰ Time Analysis")
            table_lines.append("| Metric | Value | Description |")
            table_lines.append("|--------|-------|-------------|")
            table_lines.append(f"| **Start Date** | {metrics.get('Start', 'N/A')} | Strategy start date |")
            table_lines.append(f"| **End Date** | {metrics.get('End', 'N/A')} | Strategy end date |")
            table_lines.append(f"| **Duration** | {metrics.get('Duration', 'N/A')} | Total testing period |")
            table_lines.append(f"| **Exposure Time** | {metrics.get('Exposure Time [%]', 0):.1f}% | Time in market |")
            table_lines.append("")
            
            # Additional Metrics (if available)
            additional_metrics = self._get_additional_metrics(stats)
            if additional_metrics:
                table_lines.append("### 🔬 Advanced Metrics")
                table_lines.append("| Metric | Value | Description |")
                table_lines.append("|--------|-------|-------------|")
                for metric_name, metric_value, description in additional_metrics:
                    table_lines.append(f"| **{metric_name}** | {metric_value} | {description} |")
                table_lines.append("")
            
            # Performance Summary
            table_lines.append("### 📋 Performance Summary")
            performance_grade = self._calculate_performance_grade(metrics)
            table_lines.append(f"**Overall Grade**: {performance_grade['grade']} ({performance_grade['score']:.1f}/100)")
            table_lines.append("")
            table_lines.append("**Key Strengths:**")
            for strength in performance_grade['strengths']:
                table_lines.append(f"- ✅ {strength}")
            table_lines.append("")
            table_lines.append("**Areas for Improvement:**")
            for weakness in performance_grade['weaknesses']:
                table_lines.append(f"- ⚠️ {weakness}")
            table_lines.append("")
            
            return "\n".join(table_lines)
            
        except Exception as e:
            self.logger.error(f"❌ Error generating metrics table: {e}")
            return f"❌ **Error generating metrics**: {str(e)}"
    
    def _extract_all_backtesting_metrics(self, stats) -> Dict[str, Any]:
        """Extract all available metrics from backtesting.py stats"""
        metrics = {}
        
        # Standard backtesting.py metrics
        standard_metrics = [
            'Start', 'End', 'Duration', 'Exposure Time [%]', 'Equity Final [$]',
            'Equity Peak [$]', 'Return [%]', 'Buy & Hold Return [%]', 'Return (Ann.) [%]',
            'Volatility (Ann.) [%]', 'Sharpe Ratio', 'Sortino Ratio', 'Calmar Ratio',
            'Max. Drawdown [%]', 'Avg. Drawdown [%]', 'Max. Drawdown Duration',
            'Avg. Drawdown Duration', '# Trades', 'Win Rate [%]', 'Best Trade [%]',
            'Worst Trade [%]', 'Avg. Trade [%]', 'Max. Trade Duration',
            'Avg. Trade Duration', 'Profit Factor', 'Expectancy [%]', 'SQN'
        ]
        
        # Alternative metric names (for compatibility)
        alternative_names = {
            'CAGR [%]': 'Return (Ann.) [%]',
            'Volatility [%]': 'Volatility (Ann.) [%]'
        }
        
        # Extract standard metrics
        for metric in standard_metrics:
            try:
                if hasattr(stats, 'get'):
                    metrics[metric] = stats.get(metric, 0)
                elif hasattr(stats, metric.replace(' ', '_').replace('[%]', '_pct').replace('[$]', '_dollars')):
                    attr_name = metric.replace(' ', '_').replace('[%]', '_pct').replace('[$]', '_dollars')
                    metrics[metric] = getattr(stats, attr_name, 0)
                else:
                    metrics[metric] = 0
            except:
                metrics[metric] = 0
        
        # Add alternative names
        for alt_name, original_name in alternative_names.items():
            if original_name in metrics:
                metrics[alt_name] = metrics[original_name]
        
        return metrics
    
    def _get_additional_metrics(self, stats) -> List[tuple]:
        """Get additional metrics if available"""
        additional = []
        
        try:
            # Check for additional metrics that might be available
            if hasattr(stats, '_trades') and not stats._trades.empty:
                trades_df = stats._trades
                
                # Calculate additional trade-based metrics
                if len(trades_df) > 0:
                    # Consecutive wins/losses
                    wins = trades_df['PnL'] > 0
                    consecutive_wins = self._max_consecutive(wins)
                    consecutive_losses = self._max_consecutive(~wins)
                    
                    additional.append(("Max Consecutive Wins", consecutive_wins, "Longest winning streak"))
                    additional.append(("Max Consecutive Losses", consecutive_losses, "Longest losing streak"))
                    
                    # Average win vs average loss
                    winning_trades = trades_df[trades_df['PnL'] > 0]['PnL']
                    losing_trades = trades_df[trades_df['PnL'] < 0]['PnL']
                    
                    if len(winning_trades) > 0:
                        avg_win = winning_trades.mean()
                        additional.append(("Avg Winning Trade", f"{avg_win:.2f}", "Average profit per winning trade"))
                    
                    if len(losing_trades) > 0:
                        avg_loss = abs(losing_trades.mean())
                        additional.append(("Avg Losing Trade", f"{avg_loss:.2f}", "Average loss per losing trade"))
                        
                        if len(winning_trades) > 0:
                            win_loss_ratio = avg_win / avg_loss
                            additional.append(("Win/Loss Ratio", f"{win_loss_ratio:.2f}", "Average win vs average loss"))
            
            # Check for equity curve metrics
            if hasattr(stats, '_equity_curve') and not stats._equity_curve.empty:
                equity_df = stats._equity_curve
                
                # Recovery factor (total return / max drawdown)
                total_return = stats.get('Return [%]', 0)
                max_dd = stats.get('Max. Drawdown [%]', 1)  # Avoid division by zero
                if max_dd > 0:
                    recovery_factor = total_return / max_dd
                    additional.append(("Recovery Factor", f"{recovery_factor:.2f}", "Return / Max Drawdown"))
        
        except Exception as e:
            self.logger.warning(f"Could not calculate additional metrics: {e}")
        
        return additional
    
    def _max_consecutive(self, boolean_series) -> int:
        """Calculate maximum consecutive True values in a boolean series"""
        if len(boolean_series) == 0:
            return 0
        
        max_consecutive = 0
        current_consecutive = 0
        
        for value in boolean_series:
            if value:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _calculate_performance_grade(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall performance grade based on key metrics"""
        score = 0
        strengths = []
        weaknesses = []
        
        # Return score (0-25 points)
        total_return = metrics.get('Return [%]', 0)
        if total_return > 20:
            score += 25
            strengths.append("Excellent returns (>20%)")
        elif total_return > 10:
            score += 20
            strengths.append("Good returns (10-20%)")
        elif total_return > 5:
            score += 15
        elif total_return > 0:
            score += 10
        else:
            weaknesses.append("Negative returns")
        
        # Sharpe ratio score (0-25 points)
        sharpe = metrics.get('Sharpe Ratio', 0)
        if sharpe > 2:
            score += 25
            strengths.append("Excellent risk-adjusted returns (Sharpe > 2)")
        elif sharpe > 1:
            score += 20
            strengths.append("Good risk-adjusted returns (Sharpe > 1)")
        elif sharpe > 0.5:
            score += 15
        elif sharpe > 0:
            score += 10
        else:
            weaknesses.append("Poor risk-adjusted returns (Sharpe ≤ 0)")
        
        # Win rate score (0-25 points)
        win_rate = metrics.get('Win Rate [%]', 0)
        if win_rate > 60:
            score += 25
            strengths.append("High win rate (>60%)")
        elif win_rate > 50:
            score += 20
            strengths.append("Good win rate (>50%)")
        elif win_rate > 40:
            score += 15
        elif win_rate > 30:
            score += 10
        else:
            weaknesses.append("Low win rate (≤30%)")
        
        # Drawdown score (0-25 points)
        max_dd = metrics.get('Max. Drawdown [%]', 100)
        if max_dd < 5:
            score += 25
            strengths.append("Low drawdown (<5%)")
        elif max_dd < 10:
            score += 20
            strengths.append("Moderate drawdown (<10%)")
        elif max_dd < 20:
            score += 15
        elif max_dd < 30:
            score += 10
        else:
            weaknesses.append("High drawdown (≥30%)")
        
        # Determine grade
        if score >= 90:
            grade = "A+"
        elif score >= 80:
            grade = "A"
        elif score >= 70:
            grade = "B+"
        elif score >= 60:
            grade = "B"
        elif score >= 50:
            grade = "C+"
        elif score >= 40:
            grade = "C"
        else:
            grade = "D"
        
        return {
            'score': score,
            'grade': grade,
            'strengths': strengths,
            'weaknesses': weaknesses
        }

# REMOVED: Wrapper function - Cortex will use MetricsGenerator() directly
