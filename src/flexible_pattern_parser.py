#!/usr/bin/env python3
"""
🎯 FLEXIBLE PATTERN PARSER

A robust, field-based parser that doesn't rely on specific header formats.
Extracts trading patterns based on field content rather than rigid regex patterns.
"""

import re
from dataclasses import dataclass
from typing import List, Optional, Callable, Dict, Any
import pandas as pd

# Optional config import for testing compatibility
try:
    from config import config
    WALKFORWARD_MIN_MULTIPLIER = config.WALKFORWARD_MIN_MULTIPLIER
except ImportError:
    # Default value for testing
    WALKFORWARD_MIN_MULTIPLIER = 1.5


class FlexiblePatternParseError(Exception):
    """Exception raised when flexible pattern parsing fails"""
    pass


@dataclass
class TradingPattern:
    """Flexible trading pattern representation"""
    pattern_id: int
    name: str
    entry_logic: str
    direction: str
    stop_logic: str
    target_logic: str
    position_size: float = 1.0
    timeframe: str = "5min"
    raw_text: str = ""


class FlexiblePatternParser:
    """Flexible parser that extracts patterns based on field content"""
    
    def __init__(self):
        self.patterns = []
        self.validation_errors = []
    
    def parse_llm_response(self, llm_response: str) -> List[TradingPattern]:
        """Parse LLM response using flexible field-based extraction"""
        self.patterns = []
        self.validation_errors = []
        
        # Extract all field-value pairs from the response
        field_blocks = self._extract_field_blocks(llm_response)
        
        # Group fields into patterns
        pattern_groups = self._group_fields_into_patterns(field_blocks)
        
        # Create pattern objects
        for i, group in enumerate(pattern_groups, 1):
            try:
                pattern = self._create_pattern_from_fields(i, group)
                if pattern:
                    self.patterns.append(pattern)
            except Exception as e:
                self.validation_errors.append(f"Pattern {i} creation failed: {str(e)}")
        
        if not self.patterns and self.validation_errors:
            raise FlexiblePatternParseError(f"All patterns failed to parse: {'; '.join(self.validation_errors)}")
        
        return self.patterns
    
    def _extract_field_blocks(self, text: str) -> List[Dict[str, str]]:
        """Extract all field-value pairs from text"""
        field_patterns = {
            'entry_logic': [
                r'\*\*Entry Logic:\*\*\s*([^\n]+)',
                r'Entry Logic:\s*([^\n]+)',
                r'Entry:\s*([^\n]+)',
                r'Entry Condition:\s*([^\n]+)',
            ],
            'direction': [
                r'\*\*Direction:\*\*\s*([^\n]+)',
                r'Direction:\s*([^\n]+)',
                r'Trade Direction:\s*([^\n]+)',
                r'Side:\s*([^\n]+)',
            ],
            'stop_logic': [
                r'\*\*Stop Logic:\*\*\s*([^\n]+)',
                r'Stop Logic:\s*([^\n]+)',
                r'Stop Loss:\s*([^\n]+)',
                r'Stop:\s*([^\n]+)',
            ],
            'target_logic': [
                r'\*\*Target Logic:\*\*\s*([^\n]+)',
                r'Target Logic:\s*([^\n]+)',
                r'Take Profit:\s*([^\n]+)',
                r'Target:\s*([^\n]+)',
            ],
            'position_size': [
                r'\*\*Position Size:\*\*\s*([^\n]+)',
                r'Position Size:\s*([^\n]+)',
                r'Size:\s*([^\n]+)',
            ],
            'timeframe': [
                r'\*\*Timeframe:\*\*\s*([^\n]+)',
                r'Timeframe:\s*([^\n]+)',
                r'Time Frame:\s*([^\n]+)',
            ],
            'name': [
                r'###\s*PATTERN\s*\[[XY]\]:\s*([^\n]+)',
                r'(?:###|##|\*\*)\s*(?:PATTERN\s*(?:\d+|\[[XY]\])|Pattern\s*\d+):\s*([^\n]+)',
                r'(?:Name|Title):\s*([^\n]+)',
            ]
        }
        
        all_fields = []
        
        # Extract each field type
        for field_name, patterns in field_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    # Clean up the extracted value
                    value = match.group(1).strip()
                    # Remove markdown formatting but preserve mathematical operators
                    value = re.sub(r'\*\*([^*\n]+?)\*\*', r'\1', value)  # Remove **bold**
                    value = re.sub(r'(?<!\w)\*([^*\n]+?)\*(?!\w)', r'\1', value)  # Remove *italic* but not math *
                    # Clean up any remaining formatting asterisks at start/end
                    value = re.sub(r'^\*+\s*', '', value)
                    value = re.sub(r'\s*\*+$', '', value)
                    value = value.strip()

                    field_info = {
                        'field': field_name,
                        'value': value,
                        'position': match.start(),
                        'raw_match': match.group(0)
                    }
                    all_fields.append(field_info)
        
        return all_fields
    
    def _group_fields_into_patterns(self, field_blocks: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Group fields into logical patterns based on proximity and pattern boundaries"""
        if not field_blocks:
            return []

        # Sort by position in text
        field_blocks.sort(key=lambda x: x['position'])

        patterns = []
        current_pattern = {}

        # Look for pattern boundaries (name fields or large position gaps)
        for i, field in enumerate(field_blocks):
            field_name = field['field']
            field_value = field['value']

            # Start a new pattern if we see a name field
            if field_name == 'name':
                if current_pattern:  # Save current pattern if it has content
                    patterns.append(current_pattern.copy())
                current_pattern = {'name': field_value}
                continue

            # Check for large position gaps (indicates new pattern)
            if i > 0 and field['position'] - field_blocks[i-1]['position'] > 500:
                if current_pattern:
                    patterns.append(current_pattern.copy())
                current_pattern = {}

            # If we already have this field type and it's not a duplicate, start new pattern
            if field_name in current_pattern and field_value != current_pattern[field_name]:
                if current_pattern:
                    patterns.append(current_pattern.copy())
                current_pattern = {}

            current_pattern[field_name] = field_value

        # Add the last pattern
        if current_pattern:
            patterns.append(current_pattern)

        # Filter patterns that have minimum required fields
        valid_patterns = []
        for pattern in patterns:
            if self._has_minimum_fields(pattern):
                valid_patterns.append(pattern)

        # If no valid patterns found, try a simpler approach
        if not valid_patterns:
            # Group by every 4-6 fields (typical pattern size)
            simple_patterns = []
            for i in range(0, len(field_blocks), 4):
                chunk = field_blocks[i:i+6]
                pattern = {}
                for field in chunk:
                    pattern[field['field']] = field['value']
                if self._has_minimum_fields(pattern):
                    simple_patterns.append(pattern)
            valid_patterns = simple_patterns

        return valid_patterns
    
    def _has_minimum_fields(self, pattern: Dict[str, str]) -> bool:
        """Check if pattern has minimum required fields"""
        required = ['entry_logic', 'direction']
        optional_but_helpful = ['stop_logic', 'target_logic']
        
        # Must have required fields
        for field in required:
            if field not in pattern:
                return False
        
        # Should have at least one optional field
        has_optional = any(field in pattern for field in optional_but_helpful)
        return has_optional
    
    def _create_pattern_from_fields(self, pattern_id: int, fields: Dict[str, str]) -> Optional[TradingPattern]:
        """Create a TradingPattern from extracted fields"""
        
        # Extract and validate required fields
        entry_logic = fields.get('entry_logic', '').strip()
        direction = fields.get('direction', '').strip().lower()
        
        if not entry_logic or not direction:
            raise FlexiblePatternParseError(f"Missing required fields: entry_logic or direction")
        
        # Validate direction
        if direction not in ['long', 'short', 'buy', 'sell']:
            raise FlexiblePatternParseError(f"Invalid direction: {direction}")
        
        # Normalize direction
        if direction in ['buy']:
            direction = 'long'
        elif direction in ['sell']:
            direction = 'short'
        
        # Extract optional fields with defaults
        stop_logic = fields.get('stop_logic', 'previous_low' if direction == 'long' else 'previous_high')
        target_logic = fields.get('target_logic', 'entry_price + (entry_price - stop_price) * 2.0')
        
        # Parse position size
        position_size_str = fields.get('position_size', '1')
        try:
            position_size = float(re.search(r'(\d+\.?\d*)', position_size_str).group(1))
            position_size = max(1.0, float(int(position_size)))  # Ensure whole number >= 1
        except (ValueError, TypeError, AttributeError):
            position_size = 1.0
        
        # Extract timeframe
        timeframe = fields.get('timeframe', '5min')
        
        # Extract name
        name = fields.get('name', f'Pattern {pattern_id}')
        
        return TradingPattern(
            pattern_id=pattern_id,
            name=name,
            entry_logic=entry_logic,
            direction=direction,
            stop_logic=stop_logic,
            target_logic=target_logic,
            position_size=position_size,
            timeframe=timeframe,
            raw_text=str(fields)
        )
    
    def generate_python_functions(self) -> List[Callable]:
        """Generate Python functions for backtesting"""
        functions = []
        
        for pattern in self.patterns:
            func = self._create_python_function(pattern)
            if func:
                functions.append(func)
        
        return functions
    
    def _create_python_function(self, pattern: TradingPattern) -> Optional[Callable]:
        """Create a Python function from a trading pattern"""
        
        def pattern_function(data, current_idx):
            """Generated pattern function"""
            if current_idx < 1 or current_idx >= len(data):
                return None
            
            current = data.iloc[current_idx]
            previous = data.iloc[current_idx - 1]
            
            # Evaluate entry condition
            if not self._evaluate_entry_condition(pattern.entry_logic, current, previous):
                return None
            
            # Calculate prices
            entry_price = current['Close']
            stop_price = self._calculate_stop_price(pattern.stop_logic, entry_price, current, previous, pattern.direction)
            target_price = self._calculate_target_price(pattern.target_logic, entry_price, stop_price, pattern.direction)
            
            if stop_price is None or target_price is None:
                return None
            
            # Validate order logic
            if pattern.direction == 'long':
                if stop_price >= entry_price or target_price <= entry_price:
                    return None
            else:  # short
                if stop_price <= entry_price or target_price >= entry_price:
                    return None
            
            return {
                'entry_price': entry_price,
                'stop_loss': stop_price,
                'take_profit': target_price,
                'direction': pattern.direction,
                'position_size': pattern.position_size,
                'rule_id': pattern.pattern_id
            }
        
        return pattern_function
    
    def _evaluate_entry_condition(self, entry_logic: str, current: pd.Series, previous: pd.Series) -> bool:
        """Evaluate entry condition with comprehensive logic support"""
        entry_logic = entry_logic.strip().replace('`', '').lower()

        # Simple price comparisons
        if 'current_close > previous_high' in entry_logic:
            return current['Close'] > previous['High']
        elif 'current_close < previous_low' in entry_logic:
            return current['Close'] < previous['Low']
        elif 'current_close > previous_close' in entry_logic:
            return current['Close'] > previous['Close']
        elif 'current_close < previous_close' in entry_logic:
            return current['Close'] < previous['Close']
        
        # Range-based comparisons (volatility expansion/contraction)
        elif 'current_range > previous_range' in entry_logic:
            current_range = current['High'] - current['Low']
            previous_range = previous['High'] - previous['Low']
            
            # Extract multiplier if present
            multiplier_match = re.search(r'\*\s*(\d+\.?\d*)', entry_logic)
            multiplier = float(multiplier_match.group(1)) if multiplier_match else 1.0
            
            return current_range > (previous_range * multiplier)
        
        # Gap-based comparisons
        elif 'current_open > previous_close' in entry_logic:
            gap_match = re.search(r'\+\s*(\d+\.?\d*)', entry_logic)
            gap_size = float(gap_match.group(1)) if gap_match else 0.0
            return current['Open'] > (previous['Close'] + gap_size)
        
        elif 'current_open < previous_close' in entry_logic:
            gap_match = re.search(r'-\s*(\d+\.?\d*)', entry_logic)
            gap_size = float(gap_match.group(1)) if gap_match else 0.0
            return current['Open'] < (previous['Close'] - gap_size)

        # Default: try to evaluate as simple comparison
        try:
            # Replace variables with actual values
            logic = entry_logic.replace('current_close', f" {current['Close']} ")
            logic = logic.replace('previous_high', f" {previous['High']} ")
            logic = logic.replace('previous_low', f" {previous['Low']} ")
            logic = logic.replace('previous_close', f" {previous['Close']} ")
            logic = logic.replace('current_high', f" {current['High']} ")
            logic = logic.replace('current_low', f" {current['Low']} ")
            logic = logic.replace('current_open', f" {current['Open']} ")
            
            # Handle range calculations
            current_range = current['High'] - current['Low']
            previous_range = previous['High'] - previous['Low']
            logic = logic.replace('current_range', f" {current_range} ")
            logic = logic.replace('previous_range', f" {previous_range} ")

            # Clean up extra spaces
            logic = ' '.join(logic.split())

            # Simple evaluation
            if '>' in logic or '<' in logic:
                return eval(logic)
        except Exception:
            pass

        return False
    
    def _calculate_stop_price(self, stop_logic: str, entry_price: float, current: pd.Series, previous: pd.Series, direction: str) -> Optional[float]:
        """Calculate stop loss price"""
        stop_logic = stop_logic.strip().replace('`', '').lower()

        if 'previous_low' in stop_logic:
            return previous['Low']
        elif 'previous_high' in stop_logic:
            return previous['High']
        elif 'current_low' in stop_logic:
            return current['Low']
        elif 'current_high' in stop_logic:
            return current['High']

        # Percentage-based
        pct_match = re.search(r'(\d+\.?\d*)%', stop_logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return entry_price * (1 - pct)
            else:
                return entry_price * (1 + pct)

        return None
    
    def _calculate_target_price(self, target_logic: str, entry_price: float, stop_price: Optional[float], direction: str) -> Optional[float]:
        """Calculate target price"""
        target_logic = target_logic.strip().replace('`', '').lower()

        # Risk-reward ratio based
        if stop_price is not None and 'entry_price' in target_logic and 'stop_price' in target_logic:
            try:
                # Extract multiplier from patterns like "* 2.0" or "* 3.0"
                multiplier_match = re.search(r'\*\s*(\d+\.?\d*)', target_logic)
                multiplier = float(multiplier_match.group(1)) if multiplier_match else 2.0
                
                # Ensure minimum multiplier for profitability
                multiplier = max(multiplier, WALKFORWARD_MIN_MULTIPLIER)
                
                if direction == 'long':
                    return entry_price + (entry_price - stop_price) * multiplier
                else:
                    return entry_price - (stop_price - entry_price) * multiplier
            except Exception:
                pass
        
        # Percentage-based
        pct_match = re.search(r'(\d+\.?\d*)%', target_logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return entry_price * (1 + pct)
            else:
                return entry_price * (1 - pct)
        
        return None


# Main functions for compatibility
def parse_flexible_patterns(llm_response: str) -> List[Callable]:
    """Parse LLM response and return pattern functions using flexible parsing"""
    parser = FlexiblePatternParser()
    try:
        parser.parse_llm_response(llm_response)
        return parser.generate_python_functions()
    except FlexiblePatternParseError as e:
        print(f"Flexible pattern parsing failed: {e}")
        return []
