#!/usr/bin/env python3
"""
🔧 LOGGING UTILITIES FOR JAEGER TRADING SYSTEM

Centralized logging utilities to replace print statements with proper logging.
This module provides standardized logging functions for different types of output.

Usage:
    from logging_utils import get_logger, log_info, log_warning, log_error, log_debug
    
    logger = get_logger(__name__)
    log_info("System starting...")
    log_warning("Potential issue detected")
    log_error("Critical error occurred")
"""

import logging
import sys
from typing import Optional
from config import config

# Global logger cache to avoid recreating loggers
_loggers = {}

def get_logger(name: str) -> logging.Logger:
    """
    Get or create a logger with standardized configuration.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    if name not in _loggers:
        logger = logging.getLogger(name)
        
        # Only configure if not already configured
        if not logger.handlers:
            # Create formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            # Set level from config
            logger.setLevel(getattr(logging, config.LOG_LEVEL.upper(), logging.INFO))
            
            # Add file handler if enabled
            if config.LOG_TO_FILE:
                file_handler = logging.FileHandler(config.LOG_FILE)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
            
            # Add console handler if enabled
            if config.LOG_TO_CONSOLE:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setFormatter(formatter)
                logger.addHandler(console_handler)
        
        _loggers[name] = logger
    
    return _loggers[name]

def log_info(message: str, logger_name: Optional[str] = None):
    """Log an info message."""
    logger = get_logger(logger_name or 'jaeger')
    logger.info(message)

def log_warning(message: str, logger_name: Optional[str] = None):
    """Log a warning message."""
    logger = get_logger(logger_name or 'jaeger')
    logger.warning(message)

def log_error(message: str, logger_name: Optional[str] = None):
    """Log an error message."""
    logger = get_logger(logger_name or 'jaeger')
    logger.error(message)

def log_debug(message: str, logger_name: Optional[str] = None):
    """Log a debug message."""
    logger = get_logger(logger_name or 'jaeger')
    logger.debug(message)

def log_critical(message: str, logger_name: Optional[str] = None):
    """Log a critical message."""
    logger = get_logger(logger_name or 'jaeger')
    logger.critical(message)

# Convenience functions for common Jaeger logging patterns
def log_system_start(component: str):
    """Log system component startup."""
    log_info(f"🚀 {component} - Starting...")

def log_system_complete(component: str):
    """Log system component completion."""
    log_info(f"✅ {component} - Completed successfully")

def log_system_error(component: str, error: str):
    """Log system component error."""
    log_error(f"❌ {component} - Error: {error}")

def log_data_processing(message: str):
    """Log data processing information."""
    log_info(f"📊 DATA: {message}")

def log_llm_interaction(message: str):
    """Log LLM interaction information."""
    log_info(f"🤖 LLM: {message}")

def log_backtesting(message: str):
    """Log backtesting information."""
    log_info(f"📈 BACKTEST: {message}")

def log_pattern_discovery(message: str):
    """Log pattern discovery information."""
    log_info(f"🔍 PATTERN: {message}")

def log_file_generation(message: str):
    """Log file generation information."""
    log_info(f"📁 FILE: {message}")

# Migration helper function
def replace_print_with_logging(print_message: str, level: str = "INFO", component: str = "SYSTEM") -> str:
    """
    Helper function to convert print statements to logging calls.
    
    Args:
        print_message: The original print message
        level: Log level (INFO, WARNING, ERROR, DEBUG)
        component: Component name for categorization
        
    Returns:
        Suggested logging replacement
    """
    level_map = {
        "INFO": "log_info",
        "WARNING": "log_warning", 
        "ERROR": "log_error",
        "DEBUG": "log_debug",
        "CRITICAL": "log_critical"
    }
    
    func = level_map.get(level.upper(), "log_info")
    return f'{func}(f"{component}: {print_message}")'

# Print statement detection patterns for migration
PRINT_PATTERNS = {
    "error": ["❌", "ERROR", "FAIL", "Exception", "Error"],
    "warning": ["⚠️", "WARNING", "WARN", "potential", "might"],
    "success": ["✅", "SUCCESS", "COMPLETE", "DONE", "FINISHED"],
    "info": ["📊", "🔍", "🤖", "📈", "📁", "INFO"],
    "debug": ["DEBUG", "TRACE", "VERBOSE"]
}

def categorize_print_statement(print_content: str) -> str:
    """
    Categorize a print statement to determine appropriate log level.
    
    Args:
        print_content: Content of the print statement
        
    Returns:
        Suggested log level
    """
    content_upper = print_content.upper()
    
    for level, patterns in PRINT_PATTERNS.items():
        if any(pattern.upper() in content_upper for pattern in patterns):
            return level.upper()
    
    return "INFO"  # Default to INFO level
