#!/usr/bin/env python3
"""
Walk-Forward Testing Module for Jaeger Trading System
Uses sklearn's TimeSeriesSplit for professional-grade walk-forward validation
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit
from backtesting import Backtest
import logging
from typing import Dict, List, Any, Optional
import warnings
from config import config

logger = logging.getLogger(__name__)

class WalkForwardTester:
    """
    Professional walk-forward testing using sklearn's TimeSeriesSplit
    
    This class provides robust walk-forward validation for trading strategies,
    following industry best practices and avoiding manual coding errors.
    """
    
    def __init__(self, n_splits: Optional[int] = None, max_train_size: Optional[int] = None, 
                 test_size: Optional[int] = None, gap: Optional[int] = None):
        """
        Initialize Walk-Forward Tester
        
        Args:
            n_splits: Number of splits for walk-forward validation (from config if None)
            max_train_size: Maximum size for training set (None = unlimited)
            test_size: Size of test set (None = auto-calculated)
            gap: Number of samples to exclude between train and test (from config if None)
        """
        self.n_splits = n_splits if n_splits is not None else config.WALKFORWARD_DEFAULT_N_SPLITS
        self.max_train_size = max_train_size
        self.test_size = test_size
        self.gap = gap if gap is not None else config.WALKFORWARD_DEFAULT_GAP
        
        # Initialize TimeSeriesSplit
        self.tscv = TimeSeriesSplit(
            n_splits=self.n_splits,
            max_train_size=self.max_train_size,
            test_size=self.test_size,
            gap=self.gap
        )
        
        self.logger = logging.getLogger(__name__)
        
    def run_walk_forward_test(self, data: pd.DataFrame, strategy_class, 
                             strategy_params: Dict[str, Any] = None,
                             backtest_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Run walk-forward testing on a trading strategy
        
        Args:
            data: OHLC data with datetime index
            strategy_class: Trading strategy class (e.g., JaegerStrategy)
            strategy_params: Parameters for the strategy
            backtest_params: Parameters for backtesting.py Backtest
            
        Returns:
            Dict containing walk-forward test results
        """
        try:
            # Validate inputs
            if not isinstance(data.index, pd.DatetimeIndex):
                raise ValueError("Data must have DatetimeIndex")
            
            required_columns = ['Open', 'High', 'Low', 'Close']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Default parameters from configuration
            strategy_params = strategy_params or {}
            backtest_params = backtest_params or {
                'cash': config.WALKFORWARD_DEFAULT_CASH,
                'commission': config.WALKFORWARD_DEFAULT_COMMISSION,
                'margin': config.WALKFORWARD_DEFAULT_MARGIN,
                'exclusive_orders': config.WALKFORWARD_DEFAULT_EXCLUSIVE_ORDERS
            }
            
            self.logger.info(f"🚀 Starting walk-forward test with {self.n_splits} splits")
            self.logger.info(f"   Data range: {data.index.min()} to {data.index.max()}")
            self.logger.info(f"   Total samples: {len(data)}")
            
            # Store results for each fold
            fold_results = []
            all_trades = []
            equity_curves = []
            
            # Run walk-forward validation
            for fold_idx, (train_idx, test_idx) in enumerate(self.tscv.split(data)):
                self.logger.info(f"📊 Processing Fold {fold_idx + 1}/{self.n_splits}")
                
                # Split data
                train_data = data.iloc[train_idx].copy()
                test_data = data.iloc[test_idx].copy()
                
                self.logger.info(f"   Train: {len(train_data)} samples ({train_data.index.min()} to {train_data.index.max()})")
                self.logger.info(f"   Test:  {len(test_data)} samples ({test_data.index.min()} to {test_data.index.max()})")
                
                # Run backtest on test data (trained conceptually on train data)
                fold_result = self._run_single_fold(
                    train_data, test_data, strategy_class, 
                    strategy_params, backtest_params, fold_idx
                )
                
                if fold_result:
                    fold_results.append(fold_result)
                    
                    # Collect trades and equity data
                    if 'trades' in fold_result:
                        all_trades.extend(fold_result['trades'])
                    
                    if 'equity_curve' in fold_result:
                        equity_curves.append(fold_result['equity_curve'])
            
            # Aggregate results
            aggregated_results = self._aggregate_results(fold_results, all_trades, equity_curves)
            
            self.logger.info(f"✅ Walk-forward test completed successfully")
            self.logger.info(f"   Average Return: {aggregated_results['summary']['avg_return']:.2f}%")
            self.logger.info(f"   Total Trades: {aggregated_results['summary']['total_trades']}")
            self.logger.info(f"   Win Rate: {aggregated_results['summary']['avg_win_rate']:.1f}%")
            
            return aggregated_results
            
        except Exception as e:
            self.logger.error(f"❌ Walk-forward test failed: {e}")
            raise
    
    def _run_single_fold(self, train_data: pd.DataFrame, test_data: pd.DataFrame,
                        strategy_class, strategy_params: Dict[str, Any],
                        backtest_params: Dict[str, Any], fold_idx: int) -> Optional[Dict[str, Any]]:
        """Run backtest for a single fold"""
        try:
            # Create backtest instance
            bt = Backtest(
                test_data,  # Test on out-of-sample data
                strategy_class,
                **backtest_params
            )
            
            # Run backtest
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")  # Suppress backtesting warnings
                stats = bt.run(**strategy_params)
            
            # Extract key metrics
            fold_result = {
                'fold': fold_idx,
                'train_period': (train_data.index.min(), train_data.index.max()),
                'test_period': (test_data.index.min(), test_data.index.max()),
                'train_samples': len(train_data),
                'test_samples': len(test_data),
                'stats': stats,
                'return_pct': stats.get('Return [%]', 0),
                'trades_count': stats.get('# Trades', 0),
                'win_rate_pct': stats.get('Win Rate [%]', 0),
                'profit_factor': stats.get('Profit Factor', 0),
                'max_drawdown_pct': stats.get('Max. Drawdown [%]', 0),
                'sharpe_ratio': stats.get('Sharpe Ratio', 0),
                'start_date': stats.get('Start', None),
                'end_date': stats.get('End', None)
            }
            
            # Extract trades if available
            if hasattr(stats, '_trades') and not stats._trades.empty:
                trades_df = stats._trades.copy()
                trades_df['fold'] = fold_idx
                fold_result['trades'] = trades_df.to_dict('records')
            
            # Extract equity curve if available
            if hasattr(stats, '_equity_curve') and not stats._equity_curve.empty:
                equity_df = stats._equity_curve.copy()
                equity_df['fold'] = fold_idx
                fold_result['equity_curve'] = equity_df
            
            return fold_result
            
        except Exception as e:
            self.logger.warning(f"⚠️  Fold {fold_idx} failed: {e}")
            return None
    
    def _aggregate_results(self, fold_results: List[Dict[str, Any]], 
                          all_trades: List[Dict[str, Any]], 
                          equity_curves: List[pd.DataFrame]) -> Dict[str, Any]:
        """Aggregate results from all folds"""
        
        if not fold_results:
            return {'error': 'No successful folds'}
        
        # Calculate summary statistics
        returns = [r['return_pct'] for r in fold_results]
        trade_counts = [r['trades_count'] for r in fold_results]
        win_rates = [r['win_rate_pct'] for r in fold_results if r['trades_count'] > 0]
        profit_factors = [r['profit_factor'] for r in fold_results if r['profit_factor'] > 0]
        max_drawdowns = [r['max_drawdown_pct'] for r in fold_results]
        sharpe_ratios = [r['sharpe_ratio'] for r in fold_results if not np.isnan(r['sharpe_ratio'])]
        
        summary = {
            'total_folds': len(fold_results),
            'successful_folds': len([r for r in fold_results if r['trades_count'] > 0]),
            'avg_return': np.mean(returns),
            'std_return': np.std(returns),
            'min_return': np.min(returns),
            'max_return': np.max(returns),
            'total_trades': sum(trade_counts),
            'avg_trades_per_fold': np.mean(trade_counts),
            'avg_win_rate': np.mean(win_rates) if win_rates else 0,
            'avg_profit_factor': np.mean(profit_factors) if profit_factors else 0,
            'avg_max_drawdown': np.mean(max_drawdowns),
            'avg_sharpe_ratio': np.mean(sharpe_ratios) if sharpe_ratios else 0,
            'consistency_score': len([r for r in returns if r > 0]) / len(returns) * 100
        }
        
        # Combine equity curves
        combined_equity = None
        if equity_curves:
            # Handle both Series and DataFrame equity curves
            if isinstance(equity_curves[0], pd.Series):
                combined_equity = pd.concat(equity_curves, ignore_index=False)
            else:
                # For DataFrames, concatenate and then sort by index
                combined_equity = pd.concat(equity_curves, ignore_index=False)
            
            # Sort by index, handling mixed types if necessary
            try:
                combined_equity = combined_equity.sort_index()
            except TypeError:
                # If sorting fails due to mixed types, convert index to string first
                combined_equity.index = combined_equity.index.astype(str)
                combined_equity = combined_equity.sort_index()
        
        return {
            'summary': summary,
            'fold_results': fold_results,
            'all_trades': all_trades,
            'combined_equity_curve': combined_equity,
            'walk_forward_config': {
                'n_splits': self.n_splits,
                'max_train_size': self.max_train_size,
                'test_size': self.test_size,
                'gap': self.gap
            }
        }
    
    def generate_walk_forward_report(self, results: Dict[str, Any], 
                                   output_path: str = None) -> str:
        """Generate a comprehensive walk-forward testing report"""
        
        summary = results['summary']
        fold_results = results['fold_results']
        
        report = f"""# Walk-Forward Testing Report

## Configuration
- **Number of Splits**: {results['walk_forward_config']['n_splits']}
- **Max Train Size**: {results['walk_forward_config']['max_train_size'] or 'Unlimited'}
- **Test Size**: {results['walk_forward_config']['test_size'] or 'Auto-calculated'}
- **Gap**: {results['walk_forward_config']['gap']} samples

## Summary Statistics
- **Total Folds**: {summary['total_folds']}
- **Successful Folds**: {summary['successful_folds']}
- **Average Return**: {summary['avg_return']:.2f}% ± {summary['std_return']:.2f}%
- **Return Range**: {summary['min_return']:.2f}% to {summary['max_return']:.2f}%
- **Total Trades**: {summary['total_trades']}
- **Average Trades per Fold**: {summary['avg_trades_per_fold']:.1f}
- **Average Win Rate**: {summary['avg_win_rate']:.1f}%
- **Average Profit Factor**: {summary['avg_profit_factor']:.2f}
- **Average Max Drawdown**: {summary['avg_max_drawdown']:.2f}%
- **Average Sharpe Ratio**: {summary['avg_sharpe_ratio']:.2f}
- **Consistency Score**: {summary['consistency_score']:.1f}% (profitable folds)

## Fold-by-Fold Results
"""
        
        for fold in fold_results:
            report += f"""
### Fold {fold['fold'] + 1}
- **Period**: {fold['test_period'][0]} to {fold['test_period'][1]}
- **Samples**: {fold['test_samples']} (train: {fold['train_samples']})
- **Return**: {fold['return_pct']:.2f}%
- **Trades**: {fold['trades_count']}
- **Win Rate**: {fold['win_rate_pct']:.1f}%
- **Max Drawdown**: {fold['max_drawdown_pct']:.2f}%
"""
        
        report += f"""
## Interpretation
- **Consistency**: {summary['consistency_score']:.1f}% of folds were profitable
- **Stability**: Standard deviation of returns is {summary['std_return']:.2f}%
- **Risk-Adjusted Performance**: Average Sharpe ratio of {summary['avg_sharpe_ratio']:.2f}

## Recommendations
"""
        
        if summary['consistency_score'] >= 70:
            report += "✅ **Strong Performance**: Strategy shows consistent profitability across time periods.\n"
        elif summary['consistency_score'] >= 50:
            report += "⚠️  **Moderate Performance**: Strategy shows mixed results. Consider optimization.\n"
        else:
            report += "❌ **Weak Performance**: Strategy shows poor consistency. Major revision needed.\n"
        
        if summary['avg_sharpe_ratio'] >= 1.0:
            report += "✅ **Good Risk-Adjusted Returns**: Sharpe ratio indicates favorable risk/reward.\n"
        else:
            report += "⚠️  **Poor Risk-Adjusted Returns**: Consider risk management improvements.\n"
        
        # Save report if path provided
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report)
            self.logger.info(f"📄 Walk-forward report saved: {output_path}")
        
        return report

# REMOVED: Wrapper function - Cortex will use WalkForwardTester() directly
