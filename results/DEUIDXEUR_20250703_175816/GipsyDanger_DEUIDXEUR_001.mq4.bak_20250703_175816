//+------------------------------------------------------------------+
//|                  GipsyDanger_DEUIDXEUR_001.mq4                   |
//|                        Generated from Validated Backtesting Patterns |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Jaeger Trading System"
#property link      ""
#property version   "1.00"
#property strict

//--- EA Information
// This EA contains 1 validated patterns that passed walk-forward testing
// Each pattern has been proven profitable through rigorous backtesting

//+------------------------------------------------------------------+
//| Input Parameters
//+------------------------------------------------------------------+
input double LotSize = 0.1;              // Position size
input int MagicNumber = 12345;           // Magic number for orders
input bool UseTimeFilter = false;        // Enable time filtering
input int StartHour = 9;                 // Trading start hour
input int EndHour = 16:30;                  // Trading end hour

// Pattern Enable/Disable Controls
input bool EnablePattern532 = true;  // Enable London Session 30-Min ORB Breakout

//+------------------------------------------------------------------+
//| Global Variables
//+------------------------------------------------------------------+
int totalOrders = 0;
datetime lastBarTime = 0;

// ORB-specific variables
double ORB_High = 0.0;
double ORB_Low = 0.0;
datetime ORB_StartTime = 0;
int ORB_PeriodMinutes = 30;
bool ORB_Calculated = false;

//+------------------------------------------------------------------+
//| Expert initialization function
//+------------------------------------------------------------------+
int OnInit()
{
   Print("Jaeger Validated EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Jaeger Validated EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new bar
   if(Time[0] == lastBarTime)
      return;
   lastBarTime = Time[0];
   
   // Apply time filter if enabled
   if(UseTimeFilter && !IsTimeToTrade())
      return;
   
   // Check each validated pattern
   if(EnablePattern532) CheckPattern532();
}

//+------------------------------------------------------------------+
//| Pattern 532: London Session 30-Min ORB Breakout
//| Validated through walk-forward testing
//+------------------------------------------------------------------+
void CheckPattern532()
{
   // Entry condition: orb_breakout_above AND session_filter
   if(!(Close[0] > ORB_High))
      return;
   
   double entryPrice = Ask;
   double stopLoss = Low[1];
   double takeProfit = entryPrice + (entryPrice - stopLoss) * 2.5;
   
   // Validate order parameters
   if(!ValidateOrderParams(entryPrice, stopLoss, takeProfit, OP_BUY))
      return;
   
   // Calculate position size
   double positionSize = CalculatePositionSize(entryPrice, stopLoss);
   
   // Place order
   int ticket = OrderSend(Symbol(), OP_BUY, positionSize, entryPrice, 2, 
                         stopLoss, takeProfit, "Pattern532", MagicNumber, 0, clrBlue);
   
   if(ticket > 0)
   {
      totalOrders++;
      Print("Pattern532 order placed: ", ticket);
   }
   else
   {
      Print("Pattern532 order failed: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Utility Functions
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   int currentHour = Hour();
   return (currentHour >= StartHour && currentHour <= EndHour);
}

bool ValidateOrderParams(double entry, double sl, double tp, int orderType)
{
   // Simple validation - only check direction, no artificial distance limits
   if(orderType == OP_BUY)
   {
      return (sl < entry && tp > entry);
   }
   else if(orderType == OP_SELL)
   {
      return (sl > entry && tp < entry);
   }
   return false;
}

double CalculatePositionSize(double entry, double sl)
{
   double riskAmount = MathAbs(entry - sl);
   if(riskAmount == 0) return LotSize;

   // Simple fixed lot size for now
   return LotSize;
}

//+------------------------------------------------------------------+
//| ORB Calculation Functions
//+------------------------------------------------------------------+
void CalculateORB()
{
   if(ORB_Calculated) return;

   // Calculate opening range for current day
   datetime dayStart = iTime(Symbol(), PERIOD_D1, 0);
   int startBar = iBarShift(Symbol(), Period(), dayStart);

   if(startBar < 0) return;

   // Calculate number of bars for ORB period
   int orbBars = ORB_PeriodMinutes / Period();
   if(orbBars <= 0) orbBars = 1;

   // Find high and low of opening range
   ORB_High = High[startBar];
   ORB_Low = Low[startBar];

   for(int i = startBar; i >= startBar - orbBars && i >= 0; i--)
   {
      if(High[i] > ORB_High) ORB_High = High[i];
      if(Low[i] < ORB_Low) ORB_Low = Low[i];
   }

   ORB_Calculated = true;
   ORB_StartTime = TimeCurrent();
}

void ResetORBDaily()
{
   // Reset ORB calculation for new day
   if(TimeDay(TimeCurrent()) != TimeDay(ORB_StartTime))
   {
      ORB_Calculated = false;
      ORB_High = 0.0;
      ORB_Low = 0.0;
   }
}
