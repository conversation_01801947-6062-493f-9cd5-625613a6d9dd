# 🧠 J<PERSON><PERSON>ER TRADING SYSTEM - DEUIDXEUR

**Generated**: 2025-07-02 21:34:14
**System**: Jaeger AI Pattern Discovery
**Symbol**: DEUIDXEUR
**Methodology**: Situational Analysis + LLM Pattern Discovery

## 🎯 TRADING COSTS CONFIGURATION

**Spread**: 1 pip (0.0001) - Realistic trading cost
**Commission**: None
**LLM Awareness**: AI patterns account for 1-pip spread cost

---

## 🧠 LLM PATTERN ANALYSIS

Based on the provided instructions, I will translate each of the 9 sophisticated CFD profit patterns into simple backtesting-compatible rules. Please note that I'll provide a JSON output for each pattern.

**ORB PATTERN [1]: "London Rush" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "London Rush",
  "description": "Exploits institutional flows and retail FOMO after London session opening range breakout",
  "market_situation": "London session with clear 2-candle opening range establishment followed by upward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 2
    },
    {
      "condition": "session_filter",
      "value": "london"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 25
    },
    {
      "condition": "fixed_pips_target",
      "pips": 75
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.02,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "15min",
    "session": "london"
  },
  "orb_logic": "London opening creates institutional order flow that establishes clear range, breakout indicates continuation of institutional direction"
}
```

**ORB PATTERN [2]: "Asian Gap" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Asian Gap",
  "description": "Exploits institutional flows and retail FOMO after Asian session opening range breakout",
  "market_situation": "Asian session with clear 6-candle opening range establishment followed by upward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 6
    },
    {
      "condition": "session_filter",
      "value": "asian"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "asian"
  },
  "orb_logic": "Asian opening range breakout triggers institutional flows and retail FOMO"
}
```

**ORB PATTERN [3]: "NY Sell" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "NY Sell",
  "description": "Exploits institutional selling pressure after New York session opening range breakdown",
  "market_situation": "New York session with clear 3-candle opening range establishment followed by downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_below",
      "orb_period_minutes": 30,
      "orb_period_bars": 3
    },
    {
      "condition": "session_filter",
      "value": "ny"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "ny"
  },
  "orb_logic": "New York opening range breakdown triggers institutional selling pressure"
}
```

**ORB PATTERN [4]: "Range Expansion" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Range Expansion",
  "description": "Exploits institutional flows and retail FOMO after London session opening range breakout",
  "market_situation": "London session with clear 4-candle opening range establishment followed by upward or downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 4
    },
    {
      "condition": "session_filter",
      "value": "london"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "london"
  },
  "orb_logic": "London opening range breakout triggers institutional flows and retail FOMO"
}
```

**ORB PATTERN [5]: "Volatility Release" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Volatility Release",
  "description": "Exploits institutional flows and retail FOMO after Asian session opening range breakout",
  "market_situation": "Asian session with clear 6-candle opening range establishment followed by upward or downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 6
    },
    {
      "condition": "session_filter",
      "value": "asian"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "asian"
  },
  "orb_logic": "Asian opening range breakout triggers institutional flows and retail FOMO"
}
```

**ORB PATTERN [6]: "Gap Fill" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Gap Fill",
  "description": "Exploits institutional flows and retail FOMO after London session opening range breakout",
  "market_situation": "London session with clear 2-candle opening range establishment followed by upward or downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 2
    },
    {
      "condition": "session_filter",
      "value": "london"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "london"
  },
  "orb_logic": "London opening range breakout triggers institutional flows and retail FOMO"
}
```

**ORB PATTERN [7]: "Range Expansion Reversal" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Range Expansion Reversal",
  "description": "Exploits institutional selling pressure after New York session opening range breakdown",
  "market_situation": "New York session with clear 3-candle opening range establishment followed by downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_below",
      "orb_period_minutes": 30,
      "orb_period_bars": 3
    },
    {
      "condition": "session_filter",
      "value": "ny"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "ny"
  },
  "orb_logic": "New York opening range breakdown triggers institutional selling pressure"
}
```

**ORB PATTERN [8]: "Volatility Expansion" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Volatility Expansion",
  "description": "Exploits institutional flows and retail FOMO after London session opening range breakout",
  "market_situation": "London session with clear 4-candle opening range establishment followed by upward or downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 4
    },
    {
      "condition": "session_filter",
      "value": "london"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "london"
  },
  "orb_logic": "London opening range breakout triggers institutional flows and retail FOMO"
}
```

**ORB PATTERN [9]: "Range Compression" - ORB CFD PROFIT MAXIMIZER**

```json
{
  "pattern_name": "Range Compression",
  "description": "Exploits institutional selling pressure after New York session opening range breakdown",
  "market_situation": "New York session with clear 3-candle opening range establishment followed by downward breakout",
  "entry_conditions": [
    {
      "condition": "orb_breakout_below",
      "orb_period_minutes": 30,
      "orb_period_bars": 3
    },
    {
      "condition": "session_filter",
      "value": "ny"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 30
    },
    {
      "condition": "fixed_pips_target",
      "pips": 60
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.01,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "30min",
    "session": "ny"
  },
  "orb_logic": "New York opening range breakdown triggers institutional selling pressure"
}
```

Please note that I've used the same structure and format for each pattern, with some minor adjustments to accommodate specific details.

✅ **VALIDATION PASSED**: All claims verified against actual data.

---

## 📊 BACKTESTING RESULTS

**Source**: backtesting.py professional framework
**Data Quality**: Real market data with realistic spread costs


### Pattern 1 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: -5.20%
- **Annualized Return**: -5.07%
- **CAGR**: -3.63%
- **Buy & Hold Return**: 27.26%
- **Volatility (Ann.)**: 2.18%

**Risk Metrics:**
- **Max Drawdown**: -5.33%
- **Avg Drawdown**: -1.40%
- **Max DD Duration**: 359 days 10:00:00
- **Avg DD Duration**: 90 days 19:57:00
- **Exposure Time**: 4.5%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: -2.324
- **Sortino Ratio**: -2.967
- **Calmar Ratio**: -0.951
- **Alpha**: -5.65%
- **Beta**: 0.017

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $94,800.96
- **Peak Equity**: $100,093.28

**Trade Statistics:**
- **Total Trades**: 662
- **Win Rate**: 43.4%
- **Best Trade**: 0.45%
- **Worst Trade**: -0.72%
- **Average Trade**: -0.01%
- **Expectancy**: -0.01%

**Trade Quality Metrics:**
- **Profit Factor**: 0.77
- **SQN (System Quality)**: -2.36
- **Kelly Criterion**: -0.133

**Duration Analysis:**
- **Backtest Period**: 364 days 01:45:00
- **Start Date**: 2024-05-27 00:15:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 00:15:00
- **Avg Trade Duration**: 0 days 00:08:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_1_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 2 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: -4.43%
- **Annualized Return**: -4.33%
- **CAGR**: -3.09%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 1.74%

**Risk Metrics:**
- **Max Drawdown**: -4.74%
- **Avg Drawdown**: -1.68%
- **Max DD Duration**: 346 days 13:30:00
- **Avg DD Duration**: 120 days 19:40:00
- **Exposure Time**: 4.8%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: -2.491
- **Sortino Ratio**: -3.147
- **Calmar Ratio**: -0.913
- **Alpha**: -4.77%
- **Beta**: 0.013

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $95,572.31
- **Peak Equity**: $100,228.69

**Trade Statistics:**
- **Total Trades**: 337
- **Win Rate**: 35.3%
- **Best Trade**: 1.14%
- **Worst Trade**: -0.88%
- **Average Trade**: -0.01%
- **Expectancy**: -0.01%

**Trade Quality Metrics:**
- **Profit Factor**: 0.60
- **SQN (System Quality)**: -2.41
- **Kelly Criterion**: -0.231

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 00:30:00
- **Avg Trade Duration**: 0 days 00:20:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_2_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 3 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: 0.38%
- **Annualized Return**: 0.37%
- **CAGR**: 0.26%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 3.86%

**Risk Metrics:**
- **Max Drawdown**: -2.54%
- **Avg Drawdown**: -2.11%
- **Max DD Duration**: 311 days 00:00:00
- **Avg DD Duration**: 179 days 16:45:00
- **Exposure Time**: 4.0%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: 0.097
- **Sortino Ratio**: 0.186
- **Calmar Ratio**: 0.147
- **Alpha**: 1.00%
- **Beta**: -0.023

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $100,382.05
- **Peak Equity**: $102,950.42

**Trade Statistics:**
- **Total Trades**: 282
- **Win Rate**: 38.3%
- **Best Trade**: 3.51%
- **Worst Trade**: -1.63%
- **Average Trade**: 0.00%
- **Expectancy**: 0.00%

**Trade Quality Metrics:**
- **Profit Factor**: 1.06
- **SQN (System Quality)**: 0.10
- **Kelly Criterion**: 0.011

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 02:30:00
- **Avg Trade Duration**: 0 days 00:22:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_3_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 4 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: -5.86%
- **Annualized Return**: -5.79%
- **CAGR**: -4.10%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 2.49%

**Risk Metrics:**
- **Max Drawdown**: -5.93%
- **Avg Drawdown**: -5.93%
- **Max DD Duration**: 362 days 15:00:00
- **Avg DD Duration**: 362 days 15:00:00
- **Exposure Time**: 5.4%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: -2.324
- **Sortino Ratio**: -2.553
- **Calmar Ratio**: -0.977
- **Alpha**: -6.57%
- **Beta**: 0.026

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $94,136.17
- **Peak Equity**: $100,065.64

**Trade Statistics:**
- **Total Trades**: 397
- **Win Rate**: 44.8%
- **Best Trade**: 0.38%
- **Worst Trade**: -1.41%
- **Average Trade**: -0.02%
- **Expectancy**: -0.02%

**Trade Quality Metrics:**
- **Profit Factor**: 0.70
- **SQN (System Quality)**: -2.20
- **Kelly Criterion**: -0.192

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 00:30:00
- **Avg Trade Duration**: 0 days 00:17:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_4_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 5 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: -4.43%
- **Annualized Return**: -4.33%
- **CAGR**: -3.09%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 1.74%

**Risk Metrics:**
- **Max Drawdown**: -4.74%
- **Avg Drawdown**: -1.68%
- **Max DD Duration**: 346 days 13:30:00
- **Avg DD Duration**: 120 days 19:40:00
- **Exposure Time**: 4.8%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: -2.491
- **Sortino Ratio**: -3.147
- **Calmar Ratio**: -0.913
- **Alpha**: -4.77%
- **Beta**: 0.013

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $95,572.31
- **Peak Equity**: $100,228.69

**Trade Statistics:**
- **Total Trades**: 337
- **Win Rate**: 35.3%
- **Best Trade**: 1.14%
- **Worst Trade**: -0.88%
- **Average Trade**: -0.01%
- **Expectancy**: -0.01%

**Trade Quality Metrics:**
- **Profit Factor**: 0.60
- **SQN (System Quality)**: -2.41
- **Kelly Criterion**: -0.231

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 00:30:00
- **Avg Trade Duration**: 0 days 00:20:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_5_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 6 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: -5.86%
- **Annualized Return**: -5.79%
- **CAGR**: -4.10%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 2.49%

**Risk Metrics:**
- **Max Drawdown**: -5.93%
- **Avg Drawdown**: -5.93%
- **Max DD Duration**: 362 days 15:00:00
- **Avg DD Duration**: 362 days 15:00:00
- **Exposure Time**: 5.4%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: -2.324
- **Sortino Ratio**: -2.553
- **Calmar Ratio**: -0.977
- **Alpha**: -6.57%
- **Beta**: 0.026

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $94,136.17
- **Peak Equity**: $100,065.64

**Trade Statistics:**
- **Total Trades**: 397
- **Win Rate**: 44.8%
- **Best Trade**: 0.38%
- **Worst Trade**: -1.41%
- **Average Trade**: -0.02%
- **Expectancy**: -0.02%

**Trade Quality Metrics:**
- **Profit Factor**: 0.70
- **SQN (System Quality)**: -2.20
- **Kelly Criterion**: -0.192

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 00:30:00
- **Avg Trade Duration**: 0 days 00:17:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_6_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 7 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: 0.38%
- **Annualized Return**: 0.37%
- **CAGR**: 0.26%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 3.86%

**Risk Metrics:**
- **Max Drawdown**: -2.54%
- **Avg Drawdown**: -2.11%
- **Max DD Duration**: 311 days 00:00:00
- **Avg DD Duration**: 179 days 16:45:00
- **Exposure Time**: 4.0%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: 0.097
- **Sortino Ratio**: 0.186
- **Calmar Ratio**: 0.147
- **Alpha**: 1.00%
- **Beta**: -0.023

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $100,382.05
- **Peak Equity**: $102,950.42

**Trade Statistics:**
- **Total Trades**: 282
- **Win Rate**: 38.3%
- **Best Trade**: 3.51%
- **Worst Trade**: -1.63%
- **Average Trade**: 0.00%
- **Expectancy**: 0.00%

**Trade Quality Metrics:**
- **Profit Factor**: 1.06
- **SQN (System Quality)**: 0.10
- **Kelly Criterion**: 0.011

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 02:30:00
- **Avg Trade Duration**: 0 days 00:22:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_7_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 8 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: -5.86%
- **Annualized Return**: -5.79%
- **CAGR**: -4.10%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 2.49%

**Risk Metrics:**
- **Max Drawdown**: -5.93%
- **Avg Drawdown**: -5.93%
- **Max DD Duration**: 362 days 15:00:00
- **Avg DD Duration**: 362 days 15:00:00
- **Exposure Time**: 5.4%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: -2.324
- **Sortino Ratio**: -2.553
- **Calmar Ratio**: -0.977
- **Alpha**: -6.57%
- **Beta**: 0.026

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $94,136.17
- **Peak Equity**: $100,065.64

**Trade Statistics:**
- **Total Trades**: 397
- **Win Rate**: 44.8%
- **Best Trade**: 0.38%
- **Worst Trade**: -1.41%
- **Average Trade**: -0.02%
- **Expectancy**: -0.02%

**Trade Quality Metrics:**
- **Profit Factor**: 0.70
- **SQN (System Quality)**: -2.20
- **Kelly Criterion**: -0.192

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 00:30:00
- **Avg Trade Duration**: 0 days 00:17:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_8_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


### Pattern 9 Performance

**Source**: backtesting.py comprehensive statistics


**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: 0.38%
- **Annualized Return**: 0.37%
- **CAGR**: 0.26%
- **Buy & Hold Return**: 27.28%
- **Volatility (Ann.)**: 3.86%

**Risk Metrics:**
- **Max Drawdown**: -2.54%
- **Avg Drawdown**: -2.11%
- **Max DD Duration**: 311 days 00:00:00
- **Avg DD Duration**: 179 days 16:45:00
- **Exposure Time**: 4.0%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: 0.097
- **Sortino Ratio**: 0.186
- **Calmar Ratio**: 0.147
- **Alpha**: 1.00%
- **Beta**: -0.023

**Equity Analysis:**
- **Start Equity**: $,.2f
- **Final Equity**: $100,382.05
- **Peak Equity**: $102,950.42

**Trade Statistics:**
- **Total Trades**: 282
- **Win Rate**: 38.3%
- **Best Trade**: 3.51%
- **Worst Trade**: -1.63%
- **Average Trade**: 0.00%
- **Expectancy**: 0.00%

**Trade Quality Metrics:**
- **Profit Factor**: 1.06
- **SQN (System Quality)**: 0.10
- **Kelly Criterion**: 0.011

**Duration Analysis:**
- **Backtest Period**: 364 days 01:30:00
- **Start Date**: 2024-05-27 00:30:00
- **End Date**: 2025-05-26 02:00:00
- **Max Trade Duration**: 0 days 02:30:00
- **Avg Trade Duration**: 0 days 00:22:00


📊 **Interactive Chart**: `DEUIDXEUR_pattern_9_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py


---

## 🤖 MT4 EXPERT ADVISOR

**File**: `DEUIDXEUR_EA_20250702_213414.mq4`
**Compatibility**: MT4/MT5 platforms
**Implementation**: Direct LLM pattern translation

---

## 📁 FILE STRUCTURE

```
DEUIDXEUR_20250702_213414/
├── DEUIDXEUR_trading_system_20250702_213414.md    # This report
├── DEUIDXEUR_EA_20250702_213414.mq4               # MT4 Expert Advisor
├── DEUIDXEUR_pattern_*_chart.html             # Interactive charts (backtesting.py)
└── DEUIDXEUR_trades_*.csv                     # Trade data (backtesting.py)
```

---

*Generated by Jaeger AI Trading System*
*Powered by backtesting.py framework*
