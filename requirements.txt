# Jaeger Trading System - Complete Requirements
# Core Dependencies
pandas>=2.3.0
requests>=2.32.0
urllib3>=1.26.20,<2.0.0
numpy>=2.0.0
openpyxl>=3.1.5
python-dateutil>=2.9.0

# Testing Dependencies (Required for 94% test coverage)
pytest>=8.4.0
pytest-cov>=6.2.0
coverage>=7.9.0
ruff>=0.4.4

# Enhancement Dependencies (for HTML charts and walk-forward testing)
plotly>=5.0.0
scikit-learn>=1.0.0
matplotlib>=3.0.0

# Supporting Libraries
certifi>=2025.6.0
charset-normalizer>=3.4.0
idna>=3.10.0
pytz>=2025.2
tzdata>=2025.2
