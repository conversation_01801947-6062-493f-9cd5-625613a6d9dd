============================= test session starts ==============================
platform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/Jaeger
plugins: cov-6.2.1
collected 19 items

tests/test_backtesting__broker.py ...................                    [100%]

================================ tests coverage ================================
_______________ coverage: platform darwin, python 3.9.6-final-0 ________________

Name                                    Stmts   Miss  Cover   Missing
---------------------------------------------------------------------
src/backtesting/__init__.py                16      7    56%   58-73
src/backtesting/_backtest/__init__.py      82     67    18%   33-95, 101-160, 171-176
src/backtesting/_broker.py                202     34    83%   99, 143, 158, 192, 214-215, 258-275, 293-303, 319, 333-341, 355, 357, 383, 385
src/backtesting/_plotting.py               60     55     8%   9-14, 30-71, 81-113
src/backtesting/_stats.py                  74     69     7%   10, 49-209
src/backtesting/_util.py                  106     62    42%   10-12, 17-20, 25-29, 34-40, 45-63, 76, 82, 89-97, 116-122, 126, 131, 136, 144-149, 154-158, 165, 168, 172, 177
src/backtesting/backtesting.py            287    113    61%   44-47, 50, 53-56, 59-66, 116-169, 203, 227-229, 258-260, 265, 293, 298, 303, 308, 313, 329, 332, 337, 342, 347-348, 353, 358, 365-366, 369, 416-420, 436-439, 517, 554, 562, 566-569, 585, 590, 598, 610, 624, 629-631, 636, 641, 655-659, 677, 681, 692, 696, 699-710
src/backtesting/lib.py                     85     62    27%   73, 84, 94-105, 136, 153-160, 186-196, 273-325
---------------------------------------------------------------------
TOTAL                                     912    469    49%
======================== 19 passed, 1 warning in 0.77s =========================
