#!/usr/bin/env python3
import sys
import os
sys.path.append('src')
from backtesting_rule_parser import SchemaBasedPatternParser

parser = SchemaBasedPatternParser()
markdown_response = """Here is the pattern you requested:

```json
{
    "pattern_name": "Extracted Pattern",
    "entry_conditions": [{"condition": "close_above_high"}],
    "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
}
```

This pattern should work well!"""

try:
    patterns = parser.parse_llm_response(markdown_response)
    print(f'SUCCESS: Parsed {len(patterns)} patterns directly')
    print(f'Pattern name: {patterns[0].pattern_name}')
except Exception as e:
    print(f'FAILED: {e}')
