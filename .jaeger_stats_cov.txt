============================= test session starts ==============================
platform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/Jaeger
plugins: cov-6.2.1
collected 3 items

tests/test_backtesting__stats.py ...                                     [100%]

================================ tests coverage ================================
_______________ coverage: platform darwin, python 3.9.6-final-0 ________________

Name                                    Stmts   Miss  Cover   Missing
---------------------------------------------------------------------
src/backtesting/__init__.py                16      7    56%   58-73
src/backtesting/_backtest/__init__.py      82     67    18%   33-95, 101-160, 171-176
src/backtesting/_broker.py                202    178    12%   18-48, 51, 54, 66-104, 111, 119, 123, 128-129, 132-146, 150-319, 323-343, 347-370, 373-385
src/backtesting/_plotting.py               60     55     8%   9-14, 30-71, 81-113
src/backtesting/_stats.py                  74      4    95%   128-129, 143, 151
src/backtesting/_util.py                  106     74    30%   10-12, 17-20, 25-29, 34-40, 45-63, 70-72, 75-77, 82, 89-97, 104-112, 116-122, 126, 131, 136, 139, 144-149, 154-158, 165, 168, 172, 177
src/backtesting/backtesting.py            287    159    45%   44-47, 50, 53-56, 59-66, 116-169, 203, 227-229, 258-260, 265, 293, 298, 303, 308, 313, 329, 332, 337, 342, 347-348, 353, 358, 365-366, 369, 400-408, 411-413, 416-420, 433-441, 453, 464, 474, 483, 492, 496, 504, 512, 517, 531, 542-551, 554, 557-559, 562, 566-569, 575, 580, 585, 590, 598, 610, 614, 618, 624, 629-631, 636, 641, 649-650, 655-659, 664-665, 677, 681, 692, 696, 699-710
src/backtesting/lib.py                     85     62    27%   73, 84, 94-105, 136, 153-160, 186-196, 273-325
---------------------------------------------------------------------
TOTAL                                     912    606    34%
============================== 3 passed in 0.38s ===============================
