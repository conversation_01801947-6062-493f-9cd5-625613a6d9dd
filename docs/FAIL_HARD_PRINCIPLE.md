![J<PERSON><PERSON> Logo](../branding/jaeger-logo.png)

# 🔥 FAIL HARD PRINCIPLE - UNBREAKABLE RULE

## 🚨 ABSOLUTE MANDATE: NO GRACEFUL DEGRADATION

**SYSTEM EITHER WORKS EXACTLY AS DESIGNED OR FAILS COMPLETELY**

### ❌ ABSOLUTELY FORBIDDEN - ZERO TOLERANCE:

#### **Graceful Degradation**
- ❌ "Handle errors gracefully"
- ❌ "Degrade gracefully under stress"
- ❌ "Continue functioning when components fail"
- ❌ "Graceful error recovery"
- ❌ "Fallback mechanisms"
- ❌ "Error tolerance"

#### **Fallback Logic**
- ❌ Fallback configurations
- ❌ Default values when real values fail
- ❌ Alternative code paths for errors
- ❌ Backup strategies
- ❌ "Plan B" implementations
- ❌ Workaround solutions

#### **Middle Ground Responses**
- ❌ Returning `None` on errors
- ❌ Returning empty results on failures
- ❌ Partial success responses
- ❌ "Best effort" attempts
- ❌ Approximate results when exact fails
- ❌ Warning messages instead of errors

#### **Error Masking**
- ❌ `try-except` blocks that continue execution
- ❌ Catching exceptions and returning defaults
- ❌ Logging errors but continuing
- ❌ Swallowing exceptions
- ❌ Converting errors to warnings
- ❌ "Handling" errors by ignoring them

### ✅ MANDATORY BEHAVIOR - FAIL HARD:

#### **Immediate Failure**
- ✅ **RAISE EXCEPTIONS** - Never catch and continue
- ✅ **CRASH THE SYSTEM** - When something is wrong
- ✅ **FAIL FAST** - Detect problems immediately
- ✅ **FAIL LOUD** - Make failures obvious
- ✅ **FAIL COMPLETELY** - No partial functionality

#### **Error Propagation**
- ✅ **PROPAGATE ALL ERRORS** - Let them bubble up
- ✅ **CHAIN EXCEPTIONS** - Use `raise ... from e`
- ✅ **DESCRIPTIVE ERROR MESSAGES** - "FAIL HARD: [specific reason]"
- ✅ **FATAL ERROR LOGGING** - Log as FATAL, then crash
- ✅ **NO ERROR RECOVERY** - Let the system die

#### **Binary Operation**
- ✅ **WORKS PERFECTLY** - System functions exactly as designed
- ✅ **FAILS COMPLETELY** - System stops all operation
- ✅ **NO MIDDLE STATE** - No "partially working" mode
- ✅ **ALL OR NOTHING** - Complete success or complete failure

### 🔒 ENFORCEMENT MECHANISMS:

#### **Code Review Blockers**
- Any `try-except` that continues execution after error
- Any function returning `None` or defaults on error
- Any "graceful" error handling
- Any fallback logic or alternative paths
- Any error masking or swallowing

#### **Test Requirements**
- Tests MUST verify system fails hard on invalid input
- Tests MUST use `assertRaises()` for error conditions
- Tests MUST NOT test "graceful degradation"
- Tests MUST verify complete failure, not partial success

#### **Implementation Standards**
- Use `raise RuntimeError(f"FAIL HARD: {reason}")` for all failures
- Use `raise ... from e` to chain exceptions
- Log as `logger.error(f"FATAL ERROR: {message}")` before raising
- Never return `None`, empty results, or defaults on errors
- Never catch exceptions unless re-raising immediately

### 🎯 RATIONALE:

#### **Why FAIL HARD?**
1. **RELIABILITY** - Partial failures are worse than complete failures
2. **DEBUGGING** - Hard failures are easy to diagnose and fix
3. **TRUST** - Users know exactly when system is working vs broken
4. **MAINTENANCE** - No hidden failure modes or edge cases
5. **PREDICTABILITY** - Binary behavior is completely predictable

#### **Why NO Graceful Degradation?**
1. **HIDDEN BUGS** - Graceful degradation masks real problems
2. **FALSE CONFIDENCE** - Users think system works when it's broken
3. **COMPLEXITY** - Multiple failure modes create exponential complexity
4. **UNPREDICTABILITY** - Users never know what will work vs fail
5. **TECHNICAL DEBT** - Fallback code becomes legacy burden

### 🚨 VIOLATION CONSEQUENCES:

- **IMMEDIATE CODE REJECTION** - Any graceful degradation code is rejected
- **CRITICAL BUG CLASSIFICATION** - Graceful degradation is a critical bug
- **SYSTEM REDESIGN REQUIRED** - Violating code must be completely rewritten
- **NO EXCEPTIONS** - This principle has zero exceptions or special cases

### 📋 IMPLEMENTATION CHECKLIST:

- [x] All error conditions raise exceptions
- [x] No `try-except` blocks that continue execution
- [x] No fallback configurations or default values
- [x] No `None` returns on errors
- [x] No "graceful" error handling
- [x] All exceptions use "FAIL HARD:" prefix
- [x] All exceptions are chained with `from e`
- [x] Tests verify hard failures with `assertRaises()`
- [x] No partial success or "best effort" responses
- [x] **ENHANCED**: LLM connectivity checked at system startup
- [x] **ENHANCED**: Detailed error messages with troubleshooting steps
- [x] **ENHANCED**: Removed all stub methods that violated fail-hard principle
- [x] **ENHANCED**: System halts completely when LLM unavailable

### 🚀 RECENT ENHANCEMENTS (2025-06-30):

#### **Enhanced LLM Fail-Hard Implementation**
- **Startup Validation**: System now checks LLM connectivity before any processing
- **Detailed Error Messages**: Clear "FAIL HARD:" prefixed messages with specific reasons
- **Troubleshooting Guidance**: Step-by-step instructions when LLM fails
- **Stub Method Removal**: Eliminated all temporary directory fallback mechanisms
- **Complete System Halt**: No file generation when LLM unavailable

#### **Improved Error Handling**
- **Stage 1 & 2 Validation**: Both LLM stages now have comprehensive error checking
- **Response Quality Checks**: Validates LLM response length and content quality
- **Chain Exception Handling**: All errors properly chained with `raise ... from e`
- **Fatal Error Logging**: All failures logged as FATAL before system halt

---

## 🔥 REMEMBER: FAIL HARD OR DON'T FAIL AT ALL

**The system works EXACTLY as designed, or it doesn't work AT ALL.**

**NO MIDDLE GROUND. NO EXCEPTIONS. NO COMPROMISES.**
