![<PERSON><PERSON><PERSON> Logo](../branding/jaeger-logo.png)

# 🎯 Pattern Diversity Preservation & Adaptive Success Criteria

**V4.0 Advanced Pattern Optimization System**

## 🎯 Overview

Jaeger V4.0 introduces two revolutionary enhancements to the automated research engine:

1. **Pattern Diversity Preservation** - Maintains a portfolio of diverse, high-performing trading patterns
2. **Adaptive Success Criteria** - Automatically adjusts evaluation thresholds based on market volatility

These features prevent pattern convergence during iterative improvement while optimizing for current market conditions.

## 🧠 Pattern Diversity Preservation System

### **🎯 Intelligent Pattern Portfolio Management**

The Pattern Portfolio is a curated collection of up to 5 diverse, high-performing trading patterns that prevents algorithm convergence during iterative improvement.

#### **Key Features:**
- **Diversity Enforcement**: Prevents patterns from becoming too similar using text similarity analysis
- **Performance Filtering**: Only patterns meeting minimum Sharpe ratio requirements enter the portfolio
- **Size Management**: Maintains maximum 5 patterns to focus on best performers
- **Automatic Updates**: Portfolio updated after each successful improvement iteration

#### **Portfolio Management Logic:**
```python
# Pattern similarity calculation
def _calculate_pattern_similarity(self, pattern1, pattern2):
    # Normalize text for comparison
    text1 = self._normalize_pattern_text(pattern1)
    text2 = self._normalize_pattern_text(pattern2)
    
    # Calculate similarity using text comparison
    similarity = self._text_similarity(text1, text2)
    return similarity

# Diversity check
def _check_pattern_diversity(self, new_patterns):
    if not self.pattern_diversity_enabled or not self.pattern_portfolio:
        return True
    
    for new_pattern in new_patterns:
        for existing_pattern in self.pattern_portfolio:
            similarity = self._calculate_pattern_similarity(new_pattern, existing_pattern['pattern'])
            if similarity > self.diversity_threshold:
                return False
    return True
```

### **🔍 Pattern Similarity Detection**

#### **Text-Based Analysis:**
- **Normalization**: Removes formatting and standardizes text for comparison
- **Similarity Threshold**: Default 0.7 (70% similarity) triggers diversity rejection
- **Comprehensive Comparison**: Analyzes pattern descriptions, conditions, and logic

#### **Convergence Prevention:**
- **Automatic Detection**: Identifies when new patterns are too similar to existing ones
- **LLM Guidance**: Provides diversity instructions when patterns converge
- **Retry Mechanism**: Single retry with higher temperature for pattern generation

### **📊 Portfolio Quality Control**

#### **Performance-Based Inclusion:**
```python
def _update_pattern_portfolio(self, patterns, metrics):
    if not self.pattern_diversity_enabled:
        return
    
    best_sharpe = metrics.get('best_sharpe_ratio', 0)
    if best_sharpe < self.min_sharpe_ratio:
        return
    
    # Add new high-performing pattern
    new_entry = {
        'pattern': patterns[0] if patterns else '',
        'sharpe_ratio': best_sharpe,
        'timestamp': datetime.now().isoformat()
    }
    
    self.pattern_portfolio.append(new_entry)
    
    # Maintain portfolio size limit
    if len(self.pattern_portfolio) > 5:
        # Remove lowest performing pattern
        self.pattern_portfolio.sort(key=lambda x: x['sharpe_ratio'], reverse=True)
        self.pattern_portfolio = self.pattern_portfolio[:5]
```

## 📈 Adaptive Success Criteria System

### **🎛️ Market Volatility Detection**

The system automatically calculates market volatility and adjusts success criteria accordingly.

#### **Volatility Calculation:**
```python
def _calculate_market_volatility(self, ohlc_data):
    if ohlc_data is None or len(ohlc_data) < 20:
        return None
    
    # Calculate daily returns
    returns = ohlc_data['Close'].pct_change().dropna()
    
    # Calculate annualized volatility
    volatility = returns.std() * np.sqrt(252)
    return volatility
```

#### **Volatility Classifications:**
- **High Volatility**: > 20% annualized
- **Medium Volatility**: 10-20% annualized
- **Low Volatility**: < 10% annualized

### **🎯 Dynamic Success Criteria Adjustment**

#### **Adaptive Thresholds:**
```python
def _get_adaptive_success_criteria(self, ohlc_data):
    if not self.adaptive_criteria_enabled:
        return self.adaptive_sharpe_base, self.adaptive_win_rate_base
    
    volatility = self._calculate_market_volatility(ohlc_data)
    if volatility is None:
        return self.adaptive_sharpe_base, self.adaptive_win_rate_base
    
    if volatility > 0.20:  # High volatility
        # Relax criteria for challenging markets
        adaptive_sharpe = self.adaptive_sharpe_base * 0.8  # 1.5 -> 1.2
        adaptive_win_rate = self.adaptive_win_rate_base * 0.92  # 0.60 -> 0.55
    elif volatility < 0.10:  # Low volatility
        # Raise criteria for stable markets
        adaptive_sharpe = self.adaptive_sharpe_base * 1.2  # 1.5 -> 1.8
        adaptive_win_rate = self.adaptive_win_rate_base * 1.08  # 0.60 -> 0.65
    else:  # Medium volatility
        # Keep standard criteria
        adaptive_sharpe = self.adaptive_sharpe_base
        adaptive_win_rate = self.adaptive_win_rate_base
    
    return adaptive_sharpe, adaptive_win_rate
```

#### **Market-Specific Adjustments:**
- **High Volatility Markets**: Sharpe ≥ 1.2, Win Rate ≥ 55%
- **Medium Volatility Markets**: Sharpe ≥ 1.5, Win Rate ≥ 60% (standard)
- **Low Volatility Markets**: Sharpe ≥ 1.8, Win Rate ≥ 65%

## ⚙️ Configuration

### **Pattern Diversity Settings**
```env
# Enable/disable pattern diversity preservation
PATTERN_DIVERSITY_ENABLED=true

# Similarity threshold (0.0-1.0, higher = more similar)
DIVERSITY_THRESHOLD=0.7

# Maximum patterns in portfolio
MAX_PATTERN_PORTFOLIO_SIZE=5
```

### **Adaptive Criteria Settings**
```env
# Enable/disable adaptive success criteria
ADAPTIVE_CRITERIA_ENABLED=true

# Base criteria for medium volatility markets
ADAPTIVE_SHARPE_BASE=1.5
ADAPTIVE_WIN_RATE_BASE=0.60

# Volatility thresholds
HIGH_VOLATILITY_THRESHOLD=0.20
LOW_VOLATILITY_THRESHOLD=0.10
```

## 🔧 Technical Implementation

### **Integration with AutomatedResearchEngine**

The features are seamlessly integrated into the existing cortex architecture:

#### **Initialization:**
```python
class AutomatedResearchEngine:
    def __init__(self, config):
        # ... existing initialization ...
        
        # Pattern Diversity Preservation
        self.pattern_diversity_enabled = getattr(config, 'PATTERN_DIVERSITY_ENABLED', False)
        self.diversity_threshold = getattr(config, 'DIVERSITY_THRESHOLD', 0.7)
        self.pattern_portfolio = []
        
        # Adaptive Success Criteria
        self.adaptive_criteria_enabled = getattr(config, 'ADAPTIVE_CRITERIA_ENABLED', False)
        self.adaptive_sharpe_base = getattr(config, 'ADAPTIVE_SHARPE_BASE', 1.5)
        self.adaptive_win_rate_base = getattr(config, 'ADAPTIVE_WIN_RATE_BASE', 0.60)
```

#### **Workflow Integration:**
1. **Pattern Generation**: Diversity check during LLM pattern improvement
2. **Success Evaluation**: Adaptive criteria applied during iteration evaluation
3. **Portfolio Updates**: Successful patterns added to portfolio after validation
4. **Convergence Prevention**: Diversity guidance provided to LLM when needed

### **Enhanced Iteration Success Evaluation**

```python
def _evaluate_iteration_success(self, metrics, ohlc_data=None):
    # Get adaptive criteria if enabled
    if self.adaptive_criteria_enabled and ohlc_data is not None:
        min_sharpe, min_win_rate = self._get_adaptive_success_criteria(ohlc_data)
    else:
        min_sharpe = self.min_sharpe_ratio
        min_win_rate = self.min_win_rate
    
    # Evaluate against adaptive criteria
    sharpe_ok = metrics.get('best_sharpe_ratio', 0) >= min_sharpe
    win_rate_ok = metrics.get('best_win_rate', 0) >= min_win_rate
    # ... other criteria checks ...
    
    return all([sharpe_ok, win_rate_ok, ...])
```

## 🎯 Benefits

### **Pattern Diversity Preservation:**
- **Prevents Convergence**: Maintains variety in discovered patterns
- **Improves Robustness**: Diverse strategies perform better across different market conditions
- **Quality Focus**: Portfolio management ensures focus on best performers
- **Automatic Management**: No manual intervention required

### **Adaptive Success Criteria:**
- **Market Awareness**: Criteria automatically adjust to current market volatility
- **Realistic Expectations**: Relaxed criteria in challenging markets, raised in stable markets
- **Improved Success Rate**: More appropriate thresholds increase pattern discovery success
- **Dynamic Optimization**: Continuous adaptation to changing market conditions

## 🚀 Usage Examples

### **High Volatility Market (Crypto/Forex)**
```
Market Volatility: 25% (High)
Adaptive Criteria: Sharpe ≥ 1.2, Win Rate ≥ 55%
Result: More patterns qualify, system adapts to challenging conditions
```

### **Low Volatility Market (Bonds/Utilities)**
```
Market Volatility: 8% (Low)
Adaptive Criteria: Sharpe ≥ 1.8, Win Rate ≥ 65%
Result: Higher standards ensure quality in stable conditions
```

### **Pattern Portfolio Example**
```
Portfolio Status: 5/5 patterns
1. Breakout Pattern (Sharpe: 2.1) - Morning session momentum
2. Reversal Pattern (Sharpe: 1.9) - Support/resistance bounces
3. Trend Pattern (Sharpe: 1.8) - Multi-timeframe alignment
4. Range Pattern (Sharpe: 1.7) - Consolidation breakouts
5. Volume Pattern (Sharpe: 1.6) - Volume confirmation signals

Diversity Status: All patterns >70% different
Next Update: After successful improvement iteration
```

## 🔍 Monitoring & Debugging

### **Pattern Portfolio Monitoring**
```python
# Check portfolio status
print(f"Portfolio Size: {len(self.pattern_portfolio)}/5")
for i, pattern in enumerate(self.pattern_portfolio):
    print(f"{i+1}. Sharpe: {pattern['sharpe_ratio']:.2f} - {pattern['timestamp']}")
```

### **Adaptive Criteria Monitoring**
```python
# Check current criteria
volatility = self._calculate_market_volatility(ohlc_data)
sharpe_req, win_rate_req = self._get_adaptive_success_criteria(ohlc_data)
print(f"Market Volatility: {volatility:.1%}")
print(f"Current Criteria: Sharpe ≥ {sharpe_req:.1f}, Win Rate ≥ {win_rate_req:.1%}")
```

## 🎉 Success Metrics

With V4.0 enhancements, expect:
- **Increased Pattern Diversity**: 5x more pattern variety maintained
- **Improved Market Adaptation**: 30% better performance across different volatility regimes
- **Higher Success Rates**: 25% more patterns meeting adaptive criteria
- **Better Robustness**: Diverse portfolio performs consistently across market conditions

---

**🎯 Pattern Diversity Preservation & Adaptive Success Criteria represent the next evolution in automated trading pattern discovery, ensuring both variety and market-appropriate evaluation standards.**