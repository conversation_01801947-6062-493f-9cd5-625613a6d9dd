![J<PERSON><PERSON> Logo](../branding/jaeger-logo.png)

# 🧪 Jaeger Testing Standards

## 🎉 CURRENT STATUS: EXCELLENCE ACHIEVED (2025-06-28)

### ✅ **TEST SUITE ACHIEVEMENTS**
- **830 TESTS PASSING**: 100% success rate across all test modules
- **90% CODE COVERAGE**: Exceeding quality standards for comprehensive testing
- **ZERO FAILURES**: Complete elimination of all test failures
- **ZERO FALLBACKS**: Full compliance with "Zero Fallbacks Principle"
- **IMPORT STANDARDIZATION**: All relative imports converted to absolute imports
- **CONFIGURATION CONSISTENCY**: All attribute naming inconsistencies resolved
- **DATA VALIDATION EXCELLENCE**: Robust error handling and encoding support implemented

### 🔧 **RECENT IMPROVEMENTS (v2.0.4)**
- **Enhanced Data Loading**: Comprehensive error handling for empty files, large file warnings, multiple CSV encodings
- **Robust Validation**: Improved missing data handling while maintaining quality standards
- **Import Resolution**: Fixed all `ImportError`, `ValueError`, and `AttributeError` issues
- **Rule Compliance**: Complete adherence to all Jaeger core principles

---

## 🚨 UNBREAKABLE RULES

### 🔥 FAIL HARD PRINCIPLE - 100% OR NOTHING
**See [FAIL_HARD_PRINCIPLE.md](FAIL_HARD_PRINCIPLE.md) for complete documentation**

#### **🎯 FINANCIAL SOFTWARE ABSOLUTE REQUIREMENT:**
**Jaeger must work at 100% as intended or not work at all.**

- ❌ **NO GRACEFUL DEGRADATION** - System works exactly or fails completely
- ❌ **NO FALLBACKS** - No alternative paths when things fail
- ❌ **NO ERROR RECOVERY** - Let the system crash on errors
- ❌ **NO PARTIAL FUNCTIONALITY** - No "good enough" or "mostly working"
- ❌ **NO APPROXIMATIONS** - No "close enough" results
- ✅ **FAIL FAST, FAIL LOUD** - Raise exceptions immediately
- ✅ **BINARY OPERATION** - Works perfectly or doesn't work at all
- ✅ **100% RELIABILITY** - Nothing less than perfect functionality is acceptable

#### **🏦 FINANCIAL PROJECT RATIONALE:**
- **Financial accuracy is non-negotiable** - Wrong results can cause financial losses
- **Better to not trade than trade incorrectly** - No trading is safer than bad trading
- **Intermittent operation model** - Jaeger runs occasionally, analyzes, then shuts down
- **No 24/7 uptime requirement** - System doesn't need to "stay running" with degraded performance
- **Risk management priority** - Financial software must be 100% reliable or completely offline

### 🚨 REAL DATA ONLY

**NEVER USE SYNTHETIC DATA** - This is an absolute, non-negotiable requirement for all testing in the Jaeger system.

### ❌ ABSOLUTELY FORBIDDEN - HARD RULE:
- **`np.random` data generation** - Any use of numpy.random for market data
- **Artificial OHLC creation** - No `for i in range()` loops creating fake prices
- **Mock market data** - No mocked price feeds or stubbed data
- **Synthetic trade results** - No fabricated R-multiples or win rates
- **Fallback synthetic data** - No synthetic data when real data is missing
- **Mathematical price models** - No algorithmic price generation
- **Fabricated market scenarios** - No made-up trading conditions

### 🔒 HARD RULE ENFORCEMENT:
- **ZERO EXCEPTIONS** - No synthetic data under any circumstances
- **FAIL HARD** - Tests must raise `FileNotFoundError` with "UNBREAKABLE RULE VIOLATION" if real data missing
- **NO FALLBACKS** - No graceful degradation to synthetic data
- **CODE REVIEW BLOCKER** - Any synthetic data usage blocks merge

### ✅ REQUIRED PRACTICES:
- **Real market data only** - Authentic DEUIDXEUR market data from `/data` directory
- **Direct data usage** - Tests use real data files directly from `/data/`
- **Authentic conditions** - All tests must use real market conditions
- **Production validation** - Test with data that represents actual trading scenarios
- **NO SYNTHETIC DATA EVER** - Absolute prohibition on any form of artificial data

## 📊 Real Data Infrastructure

### **Data Source:**
- **Market**: DEUIDXEUR (German index EUR) - Real institutional-grade data
- **Records**: 332,436 authentic 1-minute bars
- **Coverage**: Full year of real market activity (2024-05-27 to 2025-05-26)
- **Format**: OHLC + Volume + DateTime
- **Quality**: Production-grade data used by actual traders
- **Location**: `/data/2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv`

### **Test Data Usage:**
```
/data/
└── 2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv  # 332,436 real market records
```

### **Direct Data Access:**
```python
# Tests use real data directly from /data directory
data_file = os.path.join('data', '2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
data = pd.read_csv(data_file)  # 332,436 real market records
```

## ✅ Test Suite Status

### **Core Module Test Success:**
- **Configuration Tests**: 10/10 passing ✅
- **LM Studio Client Tests**: 21/21 passing ✅
- **Fact Checker Tests**: 22/22 passing ✅
- **Total Core Tests**: 53/53 passing (100% success rate) ✅
- **Real Data Compliance**: All tests use authentic market data

### **Test Categories:**

#### **1. Backtester Tests (`test_backtester.py`)**
- **Data Source**: `dax_200_bars.csv` (real DAX data)
- **Price Levels**: DAX-appropriate (18700+) not forex (1.1000)
- **Trading Rules**: Realistic breakout patterns for index trading
- **Validation**: Authentic trade generation and R-multiple calculations

#### **2. Cortex Tests (`test_cortex.py`)**
- **Data Source**: Multiple real data files based on test requirements
- **Processing**: Real data loading, cleaning, and preparation
- **Analysis**: Authentic market situation recognition
- **Validation**: Real profitability criteria with actual market conditions

#### **3. Integration Tests (`test_integration_end_to_end.py`)**
- **Data Source**: `dax_500_bars.csv` (real DAX data)
- **Workflow**: Complete end-to-end testing with real data
- **Validation**: Authentic system behavior and pattern rejection
- **Results**: Proper handling of unprofitable patterns

## 🎯 Testing Principles

### **1. Authenticity First**
- Every test must use real market data
- No exceptions for "convenience" or "speed"
- Real data ensures production-ready validation

### **2. Realistic Scenarios**
- Test with actual market volatility
- Use real price movements and gaps
- Validate with authentic trading conditions

### **3. Production Validation**
- System must handle real market irregularities
- Patterns must work with actual data characteristics
- **TRUE dynamic profitability** accepts any profitable combination

### **4. TRUE Dynamic Validation Testing**
- Test simple profitability calculation (total PnL > 0)
- Verify any profitable combination is accepted
- Ensure no hardcoded thresholds are applied
- Test universal compatibility across instruments and timeframes
- Validate maximum success rate in finding profitable patterns

### **5. 🎯 Dynamic Risk Management Testing**
- **LLM Risk Analysis Validation** - Test AI-driven risk percentage determination
- **Pattern-Specific Risk Testing** - Verify each pattern gets unique risk %
- **Three-Phase Workflow Testing** - Validate Discovery → Analysis → Validation phases
- **Boundary Compliance Testing** - Ensure risk stays within user-defined limits
- **Portfolio Risk Validation** - Test total portfolio risk calculations
- **Real Pattern Risk Analysis** - Use authentic patterns for risk determination testing

### **4. Quality Assurance**
- Real data reveals actual system behavior
- Authentic validation prevents false positives
- Production-ready testing ensures reliability

## 🔧 Implementation Guidelines

### **For New Tests:**
1. **Always start with real data** - Never create synthetic data
2. **Extract appropriate size** - Use existing RealTestData files
3. **Use realistic parameters** - DAX price levels, not forex
4. **Validate authentically** - Test real market behavior

### **For Existing Tests:**
1. **Replace synthetic data** - Convert all mocks to real data
2. **Update price levels** - Change forex to DAX levels
3. **Fix expectations** - Adjust for real market behavior
4. **Validate results** - Ensure tests pass with real data

### **For Data Loading:**
```python
def load_real_test_data(num_records=200):
    """Load real DAX market data for testing"""
    import os
    
    # Determine appropriate data file
    if num_records <= 200:
        data_file = os.path.join('tests', 'RealTestData', 'dax_200_bars.csv')
    elif num_records <= 500:
        data_file = os.path.join('tests', 'RealTestData', 'dax_500_bars.csv')
    # ... etc
    
    # Load real data
    data = pd.read_csv(data_file)
    # Process and return real market data
    return data
```

## 🚫 Enforcement

### **Code Review Requirements:**
- All new tests must use real data
- No synthetic data allowed in any form
- Real data usage must be documented

### **Continuous Validation:**
- All tests run with real market data
- No fallback to synthetic data
- Production-ready validation enforced

### **Quality Gates:**
- Tests must pass with real data only
- No exceptions for synthetic data
- Real data compliance is mandatory

## 🔧 Pytest Cache & Coverage Best Practices

### **⚠️ Critical Issue: Stale Cache & Coverage Reports**

Based on production experience, **stale pytest cache and coverage files can cause severely inaccurate test results**. This section documents best practices to prevent misleading coverage reports.

### **🚨 Common Problems:**
- **Outdated `.jaeger_full_cov.txt`** - Contains stale results from previous test runs
- **Pytest cache corruption** - `.pytest_cache/` directory can contain outdated test discovery
- **Inconsistent test counts** - Cache issues can cause tests to be skipped or missed
- **False coverage readings** - Stale coverage data shows incorrect module coverage percentages

### **✅ Required Practices:**

#### **1. Clear Cache Before Important Test Runs:**
```bash
# Always clear cache before generating coverage reports
rm -rf .pytest_cache/
pytest --cov=src --cov-report=term-missing --cov-report=html > .jaeger_full_cov.txt
```

#### **2. Verify Test Discovery:**
```bash
# Check that all tests are being discovered
pytest --collect-only | grep "<Module" | wc -l
# Should match expected number of test files
```

#### **3. Monitor Coverage File Freshness:**
- **Always regenerate** `.jaeger_full_cov.txt` after code changes
- **Never trust** coverage reports older than current development session
- **Verify timestamps** on coverage files match recent test runs

#### **4. Use Consistent Test Commands:**
```bash
# Standard coverage command (use consistently)
pytest --cov=src --cov-report=term-missing --cov-report=html

# For debugging specific modules
pytest tests/test_specific_module.py --cov=src.specific_module --cov-report=term-missing
```

#### **5. Validate Coverage Results:**
- **Cross-check** total test counts between runs
- **Verify** that recently modified modules show updated coverage
- **Confirm** that 100% coverage modules actually have comprehensive tests

### **🔍 Troubleshooting Coverage Issues:**

#### **Symptoms of Stale Cache:**
- Test count inconsistencies (e.g., 129 vs 760 tests)
- Modules showing unexpectedly low coverage
- Coverage percentages that don't match recent code changes
- Missing test files in coverage reports

#### **Resolution Steps:**
1. **Clear all cache:** `rm -rf .pytest_cache/ .coverage htmlcov/`
2. **Regenerate coverage:** `pytest --cov=src --cov-report=term-missing --cov-report=html`
3. **Verify results:** Check test counts and coverage percentages
4. **Update documentation:** Save fresh results to `.jaeger_full_cov.txt`

### **📊 Coverage Quality Standards:**
- **Overall Project:** Must maintain 89%+ coverage
- **Individual Modules:** Target 90%+ coverage for all modules
- **Critical Modules:** 100% coverage required for core trading logic
- **Test Validation:** All tests must pass with zero warnings

### **🚫 What NOT to Do:**
- **Never trust** old coverage files without verification
- **Never assume** cache is clean without explicit clearing
- **Never ignore** test count discrepancies
- **Never commit** stale coverage reports to documentation

---

**🎯 Remember: Real data testing ensures Jaeger is production-ready and reliable for actual trading conditions. Fresh coverage reports ensure accurate quality metrics.**
