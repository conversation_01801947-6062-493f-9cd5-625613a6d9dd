# Dynamic Context Length System

## Overview

Jaeger uses a **pure dynamic approach** to set context length for any LLM model loaded in LM Studio. The system automatically uses your configured context length (64K) when the model supports it, or falls back to the model's maximum supported context length.

## How It Works

### Simple Logic
1. **Try your configured context length** (64,000 tokens from `jaeger_config.env`)
2. **Query LM Studio** for the model's actual maximum supported context length
3. **Use the smaller value** between your request and model's maximum
4. **If model info unavailable**, use your configured value and let LM Studio handle any limits

### No Hardcoded Assumptions
- ✅ **No model databases** - queries LM Studio directly
- ✅ **No hardcoded limits** - uses real-time model information
- ✅ **Works with any model** - completely model-agnostic
- ✅ **Future-proof** - automatically adapts to new models

## Configuration

### Environment Settings
```env
# Your desired context length
LLM_CONTEXT_LENGTH=64000

# Enable/disable dynamic adjustment
LLM_AUTO_ADJUST_CONTEXT=true
```

### Behavior Control
- **`LLM_AUTO_ADJUST_CONTEXT=true`**: Use dynamic detection (recommended)
- **`LLM_AUTO_ADJUST_CONTEXT=false`**: Always use fixed 64K regardless of model

## Real-World Examples

### Model Supports Your Request
```
✅ Using requested 64,000 tokens (model supports it)
```
**Result**: Uses your full 64K request

### Model Has Lower Limit
```
📊 Model qwen2.5-coder-7b-instruct supports max 32,768 tokens
🎯 Using model maximum: 32,768 tokens
```
**Result**: Uses model's maximum (32K) instead of your 64K request

### Model Info Unavailable
```
⚠️ Could not query model info from LM Studio for unknown-model
🎯 Using requested 64,000 tokens (will let LM Studio handle limits)
```
**Result**: Uses your 64K request, LM Studio will handle any errors

## Implementation

### Core Method
```python
def get_optimal_context_length(self, model_id, requested_context_length):
    """Get optimal context length: use requested or model maximum, whichever is lower"""
    model_info = self.get_model_info(model_id)
    
    if model_info and model_info.get('max_context_length'):
        max_supported = model_info['max_context_length']
        return min(requested_context_length, max_supported)
    else:
        # Fallback: use requested value
        return requested_context_length
```

### API Integration
The system queries LM Studio's REST API endpoints:
- `/api/v0/models/{model_id}` - Get detailed model information
- `/v1/models` - Fallback for basic model list

## Benefits

1. **🎯 Optimal Performance**: Each model uses maximum supported context
2. **🛡️ Error Prevention**: Never exceeds model capabilities
3. **📊 Transparency**: Clear logging shows decisions made
4. **⚙️ Configurable**: Can disable if needed
5. **🔄 Future-Proof**: Works with any LM Studio model
6. **🧹 Clean**: No hardcoded assumptions or model databases

## LM Studio UI Display vs Runtime Behavior

### Important Note About LM Studio UI
The **LM Studio UI may still display 4096 tokens** as a default value even when the system is correctly sending 64K context length via API calls. This is normal behavior because:

- **UI Display**: Shows model's default or last manually configured value
- **Runtime API**: Uses the context length sent by Jaeger (your 64K)
- **What Matters**: The system logs showing `✅ Using requested 64,000 tokens (model supports it)`

### Verification
Look for these log messages to confirm the system is working:
```
✅ Using requested 64,000 tokens (model supports it)
```
or
```
📊 Model [model-name] supports max 32,768 tokens
🎯 Using model maximum: 32,768 tokens
```

## Testing & Verification

The system has been comprehensively tested and verified:
- ✅ **Real models loaded in LM Studio** (Llama 3.1, Qwen 2.5, etc.)
- ✅ **Unknown/unavailable models** (fallback behavior)
- ✅ **Various context length scenarios** (64K, 32K, 8K, 4K limits)
- ✅ **Import error fixes** (resolved relative import issues)
- ✅ **End-to-end system verification** (confirmed working in production)

### Production Verification
**Confirmed working with Llama 3.1 8B Instruct**:
```
🔍 Testing model: meta-llama-3.1-8b-instruct
✅ Model meta-llama-3.1-8b-instruct is working!
✅ Using requested 64,000 tokens (model supports it)
```

All tests confirm the system works purely dynamically without any hardcoded model assumptions.

## Troubleshooting

### Common Issues and Solutions

#### "attempted relative import beyond top-level package"
**Fixed**: Changed relative imports to absolute imports in `lm_studio_client.py`
```python
# OLD (broken): from ..config import config
# NEW (working): from config import config
```

#### LM Studio UI Still Shows 4096
**Normal**: UI display doesn't reflect runtime API parameters. Check system logs for actual context length being used.

#### Context Length Not Being Applied
1. **Check configuration**: Verify `LLM_CONTEXT_LENGTH=64000` in `jaeger_config.env`
2. **Check auto-adjust setting**: Ensure `LLM_AUTO_ADJUST_CONTEXT=true`
3. **Check system logs**: Look for context length messages during model testing
4. **Restart LM Studio**: Sometimes needed after configuration changes

#### Model Doesn't Support 64K
**Expected**: System will automatically use model's maximum supported context length and log the adjustment.

### Debug Commands
Test the system manually:
```bash
cd /Users/<USER>/Jaeger
source llm_env/bin/activate
python -c "
from src.config import config
from src.ai_integration.lm_studio_client import LMStudioClient
client = LMStudioClient('http://localhost:1234')
print(f'Config: {config.LLM_CONTEXT_LENGTH:,} tokens')
# Test with your loaded model
optimal = client.get_optimal_context_length('your-model-name', config.LLM_CONTEXT_LENGTH)
print(f'Optimal: {optimal:,} tokens')
"
```
