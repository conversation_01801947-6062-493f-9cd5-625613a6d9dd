![<PERSON><PERSON><PERSON>](../branding/jaeger-logo.png)

# Trading Pattern Discovery Framework: Following <PERSON>'s Methodology

## Core Mission
You are tasked with discovering novel, profitable trading patterns using <PERSON>'s systematic approach to pattern identification. Your goal is NOT to replicate his existing patterns, but to apply his discovery methodology to uncover completely new patterns that have never been documented before.

## Fundamental Philosophy to Adopt

### Situational Analysis Over Prediction
- Focus on identifying recurring market situations rather than trying to predict market direction
- Develop statistical frameworks based on what actually happens in markets, not what should happen
- Think in terms of "When X situation occurs, Y tends to follow with Z frequency"
- Remember: "Anything can happen" on any individual trade, but patterns provide statistical edges over time

### Pure Price Action Foundation
- Use ONLY Open, High, Low, Close data - no indicators whatsoever
- Rely on human pattern recognition and market context understanding
- Focus on geometric relationships and price behavior patterns
- Analyze "naked charts" to see what price is actually doing

## Discovery Methodology to Follow

### 1. Manual Research Process
- Conduct extensive historical chart analysis spanning multiple years
- Examine thousands of trading sessions manually (not algorithmic backtesting)
- Look for recurring price behaviors that happen with measurable frequency
- Cross-correlate patterns across different market conditions and timeframes
- Document pattern frequency and success rates through manual tracking

### 2. Statistical Validation Requirements
- Identify patterns that occur with consistent frequency (minimum hundreds of occurrences)
- Calculate actual win rates and risk-reward ratios
- Validate patterns across different market regimes (trending, ranging, volatile, quiet)
- Ensure patterns maintain edge across extended time periods
- Aim for statistical significance (60%+ win rates for viable patterns)

### 3. Situational Context Integration
- Understand WHEN patterns work best (specific market hours, days, conditions)
- Recognize that patterns are situational, not mechanical
- Identify market memory effects (how markets react to previous important levels)
- Consider multi-timeframe context and trend alignment
- Analyze market regime sensitivity (bull/bear markets, high/low volatility periods)

## Pattern Categories to Explore

### Time-Based Behavioral Patterns
- Investigate opening behaviors, closing behaviors, specific hour tendencies
- Analyze multi-day relationships (Monday-Friday correlations, week-to-week patterns)
- Explore intraday volatility cycles and their exploitable characteristics
- Look for seasonal or cyclical behavioral patterns

### Geometric Price Relationships
- Discover new harmonic or measured move relationships
- Identify novel support/resistance formation patterns  
- Find unique breakout or breakdown configurations
- Explore new reversal or continuation formations

### Market Memory and Context Patterns
- Investigate how markets react to previous important price levels
- Analyze gap behavior and follow-through patterns
- Study range expansion and contraction cycles
- Examine multi-session price relationships

### Volatility and Momentum Patterns
- Identify unique volatility expansion/contraction signals
- Discover momentum exhaustion patterns
- Find acceleration and deceleration signatures
- Analyze volume-price relationship patterns (if volume data available)

## Discovery Criteria

### High-Probability Setup Requirements
- Pattern must demonstrate consistent frequency over extended periods
- Clear entry and exit rules must be definable
- Risk parameters must be quantifiable and manageable
- Pattern should provide asymmetric risk-reward opportunities
- Must work across different market instruments and conditions

### Context Sensitivity Standards
- Pattern effectiveness should be understood within market context
- Should integrate with trend analysis (fade short-term trend in direction of long-term trend)
- Must consider time-based sensitivity and optimal execution windows
- Should respect market memory and key price levels

## Implementation Framework

### Non-Mechanical Approach
- Patterns should require discretionary judgment based on market context
- Real-time assessment of pattern validity is essential
- Flexible execution based on situational factors
- Human interpretation and context reading is crucial

### Risk Management Integration
- Focus on "tight loss, wide win" principle
- Quick recognition and exit of failed setups
- Capital preservation for high-probability opportunities
- Position sizing based on pattern reliability and market conditions

## Your Specific Task

Using this methodology, discover and document 3-5 completely novel trading patterns that:

1. **Have never been documented before** - These should be your original discoveries
2. **Follow Hougaard's pure price action principles** - No indicators, only OHLC data
3. **Are statistically validated** - Show clear frequency and success rate potential
4. **Are situationally contextualized** - Explain when and why they work
5. **Provide clear implementation guidelines** - Entry, exit, and risk management rules
6. **Demonstrate edge potential** - Explain the statistical and logical basis for the edge

For each pattern you discover:
- Name the pattern descriptively
- Explain the market situation it exploits
- Detail the statistical basis for the edge
- Provide clear identification criteria
- Outline entry and exit methodology
- Specify risk management parameters
- Explain the optimal market context for application

Remember: You are not copying existing patterns but discovering new ones using Hougaard's proven discovery methodology. Think like a market detective, using statistical analysis and situational awareness to uncover hidden edges in pure price action data.