![Jaeger Logo](../branding/jaeger-logo.png)

# 📈 HTML Chart Generation Guide

**Professional interactive visualization using Plotly for trading analysis**

## 🎯 Overview

The Jaeger system generates professional-grade interactive HTML charts using Plotly that provide comprehensive visualization of trading patterns, performance metrics, and market analysis. These charts replace static visualizations with interactive, publication-ready reports.

## 🏗️ Architecture

### **Core Component: `html_chart_generator.py`**
- **Professional Plotly Integration** - Industry-standard charting library
- **Interactive Features** - Pan, zoom, hover, export capabilities
- **Multi-Panel Layout** - Price charts, equity curves, drawdown analysis
- **Trade Visualization** - Entry/exit markers with profit/loss colors
- **Export Capabilities** - Save as images or interactive HTML

## 📊 Chart Types Generated

### **1. Comprehensive Backtest Charts**
```python
# Generated automatically for each trading rule
[SYMBOL]_rule_X_backtest.html
```

**Features:**
- **Candlestick Price Chart** - OHLC data with professional styling
- **Trade Markers** - Entry/exit points with profit/loss color coding
- **Equity Curve** - Real-time balance progression
- **Drawdown Analysis** - Peak balance tracking and drawdown periods
- **Interactive Controls** - Pan, zoom, hover for detailed information

### **2. Multi-Panel Layout**
```
┌─────────────────────────────────────┐
│     Price Chart with Trades        │  ← 50% height
├─────────────────────────────────────┤
│        Equity Curve               │  ← 30% height  
├─────────────────────────────────────┤
│       Drawdown Chart              │  ← 20% height
└─────────────────────────────────────┘
```

## 🔧 Technical Implementation

### **Chart Generation Process:**
1. **Data Preparation** - Extract OHLC data and trade results from backtesting.py
2. **Layout Creation** - Multi-panel subplot configuration
3. **Chart Population** - Add candlesticks, trade markers, equity curves
4. **Styling Application** - Professional color schemes and formatting
5. **Export Generation** - Save as interactive HTML file

### **Key Features:**

#### **Professional Candlestick Charts:**
```python
fig.add_trace(
    go.Candlestick(
        x=ohlc_data.index,
        open=ohlc_data['Open'],
        high=ohlc_data['High'],
        low=ohlc_data['Low'],
        close=ohlc_data['Close'],
        name='Price'
    )
)
```

#### **Trade Markers with Color Coding:**
```python
# Profitable trades - Green markers
# Losing trades - Red markers
# Entry/exit points clearly marked
```

#### **Interactive Equity Curves:**
```python
# Real-time balance tracking
# Drawdown period highlighting
# Peak balance visualization
```

## 📁 Generated Files

### **File Naming Convention:**
```
results/[SYMBOL]_[timestamp]/
├── [SYMBOL]_rule_1_backtest.html
├── [SYMBOL]_rule_2_backtest.html
├── [SYMBOL]_rule_3_backtest.html
└── ...
```

### **File Structure:**
- **Self-contained HTML** - No external dependencies
- **Embedded JavaScript** - Plotly.js included
- **Responsive Design** - Works on desktop and mobile
- **Export Ready** - Can be shared or published

## 🎨 Visual Features

### **Color Scheme:**
- **Profitable Trades** - Green markers and highlights
- **Losing Trades** - Red markers and highlights
- **Equity Curve** - Blue line with gradient fill
- **Drawdown Periods** - Red shaded areas
- **Price Chart** - Professional candlestick colors

### **Interactive Controls:**
- **Pan** - Click and drag to move around chart
- **Zoom** - Mouse wheel or zoom controls
- **Hover** - Detailed information on data points
- **Legend** - Toggle chart elements on/off
- **Export** - Save as PNG, SVG, or HTML

## 🚀 Usage

### **Automatic Generation:**
Charts are generated automatically during the Jaeger workflow:

1. **Pattern Discovery** - LLM discovers trading patterns
2. **Backtesting** - Professional backtesting.py validation
3. **Chart Generation** - Automatic HTML chart creation
4. **File Output** - Charts saved to results folder

### **Manual Access:**
```python
from html_chart_generator import HTMLChartGenerator

generator = HTMLChartGenerator()
chart_path = generator.generate_backtest_html_chart(
    stats=backtest_stats,
    ohlc_data=price_data,
    symbol="EURUSD",
    output_path="chart.html"
)
```

## 📊 Chart Analysis

### **What to Look For:**
- **Trade Distribution** - How trades are spread across time
- **Equity Progression** - Smooth vs volatile equity curves
- **Drawdown Patterns** - Frequency and severity of drawdowns
- **Entry/Exit Quality** - Visual assessment of trade timing
- **Market Conditions** - How patterns perform in different market states

### **Performance Indicators:**
- **Consistent Equity Growth** - Steady upward trend
- **Controlled Drawdowns** - Limited and recoverable declines
- **Good Trade Spacing** - Reasonable time between trades
- **Clear Profit/Loss Patterns** - Visible edge in trade outcomes

## 🔧 Configuration

### **Chart Customization:**
Charts use professional defaults but can be customized:

```python
# Chart dimensions
plot_width = 1200
plot_height = 800

# Color schemes
profitable_color = 'green'
losing_color = 'red'
equity_color = 'blue'
```

### **Export Options:**
- **HTML** - Interactive charts (default)
- **PNG** - Static images for reports
- **SVG** - Vector graphics for publications
- **PDF** - Print-ready documents

## ✅ Benefits

### **For Traders:**
- **Visual Pattern Analysis** - See exactly when and how patterns work
- **Performance Assessment** - Clear visualization of trading system performance
- **Risk Evaluation** - Visual drawdown analysis and risk assessment
- **Professional Presentation** - Publication-ready charts for analysis

### **For System Development:**
- **Debug Visualization** - See exactly what the system is doing
- **Performance Validation** - Visual confirmation of backtesting results
- **Pattern Optimization** - Identify areas for improvement
- **Professional Reporting** - Industry-standard visualization output

## 🎯 Integration

### **Workflow Integration:**
HTML charts are seamlessly integrated into the Jaeger workflow:

```
Pattern Discovery → Backtesting → Chart Generation → Results Output
```

### **File Organization:**
Charts are organized alongside other results:

```
results/EURUSD_20250623_120000/
├── EURUSD_trading_system_20250623_120000.md
├── EURUSD_rule_1_backtest.html  ← Interactive chart
├── EURUSD_rule_2_backtest.html  ← Interactive chart
├── EURUSD_walk_forward_report.md
└── Gipsy_Danger_015.mq4
```

---

## 🎉 Professional Visualization

The HTML chart generation system provides **professional-grade interactive visualization** that transforms raw backtesting data into **publication-ready analysis tools**. These charts enable traders and analysts to **visually assess pattern performance** with the same quality standards used in institutional trading environments.

**🚀 Every Jaeger run automatically generates these professional interactive charts for comprehensive pattern analysis.**
