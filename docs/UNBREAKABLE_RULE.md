![J<PERSON>ger Logo](../branding/jaeger-logo.png)

# 🚨 THE UNBREAKABLE RULE - CRITICAL SYSTEM PRINCIPLE

## ⚠️ **NEVER MANUALLY CODE WHAT BACKTESTING.PY ALREADY PROVIDES** ⚠️

This rule is **UNBREAKABLE**, **NON-NEGOTIABLE**, and **ABSOLUTELY CRITICAL** to the integrity of the Jaeger trading system.

## 🎯 **THE RULE IN DETAIL**

### **❌ FORBIDDEN ACTIONS**
- **NEVER** manually implement statistics calculations (Sharpe ratio, drawdown, win rate, etc.)
- **NEVER** manually create equity curve plots or charts
- **NEVER** manually track trade history or performance metrics
- **NEVER** manually implement broker simulation or order processing
- **NEVER** manually code position tracking or portfolio management
- **NEVER** manually implement any functionality that backtesting.py already provides

### **✅ REQUIRED ACTIONS**
- **ALWAYS** use backtesting.py's built-in `Backtest.run()` for all backtesting
- **ALWAYS** use `stats._equity_curve` for equity tracking
- **ALWAYS** use `stats._trades` for trade history
- **ALWAYS** use `stats.plot()` for all plotting and visualization
- **ALWAYS** use backtesting.py's built-in statistics (30+ professional metrics)
- **ALWAYS** check if backtesting.py provides the functionality before coding

## 🚨 **MAJOR VIOLATIONS FIXED IN V4.0**

### **1. Manual Statistics Implementation (MASSIVE VIOLATION)**
- **LOCATION**: `src/backtesting/_stats.py`
- **VIOLATION**: Manually reimplemented ALL statistics that backtesting.py provides
- **FIX**: Removed manual implementation, now uses vanilla backtesting.py's `compute_stats()`

### **2. Manual Equity Chart Generation (PLOTTING VIOLATION)**
- **LOCATION**: `src/cortex.py` - `_generate_equity_chart()` method
- **VIOLATION**: Manual matplotlib plotting instead of backtesting.py's built-in plotting
- **FIX**: Replaced with backtesting.py's `stats.plot()` functionality

## 📦 **VANILLA BACKTESTING.PY INTEGRATION**

### **Why Vanilla Integration?**
- **ZERO MODIFICATIONS**: Use backtesting.py exactly as designed
- **FULL FUNCTIONALITY**: Access to all built-in features and methods
- **PROFESSIONAL QUALITY**: Industry-standard statistics and visualization
- **FUTURE-PROOF**: Updates and improvements from the official library

### **Integration Details**
- **SOURCE**: Extracted from pip installation (`pip install backtesting`)
- **LOCATION**: `src/backtesting/` directory
- **VERSION**: Latest vanilla version with zero modifications
- **COMPLIANCE**: 100% adherence to original library design

## 🔍 **HOW TO VERIFY COMPLIANCE**

### **Before Adding Any Code, Ask:**
1. Does backtesting.py already provide this functionality?
2. Am I about to duplicate something the backtester does?
3. Can I use an existing backtesting.py method instead?
4. Would this violate the UNBREAKABLE RULE?

### **Red Flags (Immediate STOP):**
- Creating manual statistics calculations
- Writing custom plotting/charting code
- Implementing custom equity tracking
- Building custom trade history management
- Creating custom broker simulation
- Any code that starts with "manually calculate..."

## 🎯 **ENFORCEMENT STRATEGY**

### **Code Review Checklist:**
- [ ] No manual statistics implementations
- [ ] No manual plotting/charting code
- [ ] All equity data from `stats._equity_curve`
- [ ] All trade data from `stats._trades`
- [ ] All plotting uses `stats.plot()`
- [ ] All backtesting uses `Backtest.run()`

### **Testing Requirements:**
- All tests must use backtesting.py's built-in functionality
- No tests for manual implementations (they shouldn't exist)
- Verify integration with vanilla backtesting.py library

## 🚀 **BENEFITS OF COMPLIANCE**

### **Technical Benefits:**
- **Professional Quality**: Industry-standard statistics and metrics
- **Reliability**: Battle-tested backtesting framework
- **Performance**: Optimized C-level computations where applicable
- **Accuracy**: Proven statistical calculations
- **Completeness**: 30+ professional trading metrics

### **Development Benefits:**
- **Reduced Code**: Less code to maintain and debug
- **Faster Development**: No need to reinvent the wheel
- **Better Testing**: Focus on business logic, not infrastructure
- **Future Updates**: Automatic improvements from library updates

## ⚡ **IMMEDIATE ACTION REQUIRED**

If you find ANY violation of this rule:
1. **STOP** all development immediately
2. **REMOVE** the manual implementation
3. **REPLACE** with backtesting.py's built-in functionality
4. **TEST** the replacement thoroughly
5. **DOCUMENT** the fix in the changelog

## 🎯 **REMEMBER: THIS RULE IS UNBREAKABLE**

The UNBREAKABLE RULE exists to:
- Prevent code duplication and maintenance nightmares
- Ensure professional-quality trading system components
- Maintain consistency with industry standards
- Avoid bugs and errors from manual implementations
- Focus development effort on actual trading logic

**VIOLATION OF THIS RULE IS NOT ACCEPTABLE UNDER ANY CIRCUMSTANCES.**
