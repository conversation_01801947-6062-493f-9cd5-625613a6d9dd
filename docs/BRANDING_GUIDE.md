![Jaeger Logo](../branding/jaeger-logo.png)

# 🎨 Jaeger Branding Guide

**Official branding standards and asset protection guidelines for the Jaeger system**

## 🎯 Overview

This guide establishes the official branding standards for the Jaeger Situational Analysis Trading System. Consistent branding ensures professional presentation and system integrity across all documentation and components.

## 📁 Branding Assets

### **Critical System Files:**
- **`branding/jaeger-logo.png`** - Primary logo for documentation headers
- **`branding/jaeger-icon.png`** - Icon for scripts and system components

### **⚠️ CRITICAL WARNING:**
**These files are ESSENTIAL for system operation and must NEVER be deleted:**
- Deleting them will break documentation rendering
- Scripts and system components depend on these assets
- Always included in backups for protection
- Required for proper system functionality

## 📋 Logo Placement Standards

### **Mandatory Logo Placement:**
**ALL documentation files MUST include the Jaeger logo at the very top** using this exact format:

#### **For files in `/docs` directory:**
```markdown
![Jaeger Logo](../branding/jaeger-logo.png)

# Document Title
```

#### **For files in project root:**
```markdown
![Jaeger Logo](branding/jaeger-logo.png)

# Document Title
```

#### **For files in subdirectories (like `/tests`):**
```markdown
![Jaeger Logo](../branding/jaeger-logo.png)

# Document Title
```

### **Required Documentation Files:**
The following files MUST have the logo at the top:
- ✅ `README.md`
- ✅ `docs/README.md`
- ✅ `docs/USER_DOCUMENTATION.md`
- ✅ `docs/TECHNICAL_DOCUMENTATION.md`
- ✅ `docs/CONFIGURATION_GUIDE.md`
- ✅ `docs/CORTEX_ARCHITECTURE.md`
- ✅ `docs/PATTERN_DISCOVERY_PRINCIPLES.md`
- ✅ `docs/BRANDING_GUIDE.md` (this file)
- ✅ `docs/HTML_CHART_GENERATION.md`
- ✅ `docs/WALK_FORWARD_TESTING.md`
- ✅ `docs/PROFESSIONAL_METRICS.md`
- ✅ `docs/BEHAVIORAL_INTELLIGENCE.md`
- ✅ `docs/JAEGER_STRATEGY_INTEGRATION.md`
- ✅ `tests/README.md`

## 🎨 Visual Standards

### **Logo Usage Guidelines:**
- **Always use the full logo** - Never crop or modify
- **Maintain aspect ratio** - Never stretch or distort
- **Use original resolution** - Don't resize unnecessarily
- **Place at document top** - First element after any front matter
- **Include blank line** - Always add blank line after logo before title

### **Correct Implementation:**
```markdown
![Jaeger Logo](../branding/jaeger-logo.png)

# 🤖 Document Title
```

### **Incorrect Implementation:**
```markdown
# Document Title
![Jaeger Logo](../branding/jaeger-logo.png)  ❌ Wrong position

![Logo](../branding/jaeger-logo.png)  ❌ Wrong alt text

![Jaeger Logo](logo.png)  ❌ Wrong path
```

## 🔒 Asset Protection

### **Backup Requirements:**
- **Always include in backups** - Both manual and automated
- **Verify after restore** - Ensure assets are present and functional
- **Test documentation** - Confirm logos render properly after any changes

### **File Integrity:**
- **Never rename** - Keep original filenames exactly as specified
- **Never move** - Assets must remain in `/branding` directory
- **Never modify** - Use original files without alterations
- **Check permissions** - Ensure files are readable by system

### **System Dependencies:**
These components depend on branding assets:
- **Documentation rendering** - All `.md` files reference logo
- **Backup/restore scripts** - Include branding assets in operations
- **System integrity checks** - Validate asset presence

## 📖 Documentation Standards

### **Professional Presentation:**
- **Consistent branding** - Every document follows same format
- **Clean hierarchy** - Logo → Title → Content structure
- **Proper spacing** - Blank lines for visual separation
- **Correct paths** - Relative paths that work from file location

### **Quality Assurance:**
Before publishing any documentation:
1. ✅ **Logo present** - Verify logo appears at top
2. ✅ **Path correct** - Ensure relative path works
3. ✅ **Rendering test** - Confirm logo displays properly
4. ✅ **Alt text accurate** - Use "Jaeger Logo" exactly
5. ✅ **Spacing proper** - Blank line after logo

## 🛡️ System Protection

### **Critical File Protection:**
```
branding/
├── jaeger-logo.png     # 🚨 CRITICAL - Never delete
└── jaeger-icon.png     # 🚨 CRITICAL - Never delete
```

### **Consequences of Deletion:**
- **Documentation breaks** - Logo references will fail
- **System integrity compromised** - Missing essential assets
- **Professional appearance lost** - Inconsistent branding
- **Backup/restore issues** - System expects these files

### **Recovery Procedures:**
If branding assets are accidentally deleted:
1. **Stop all operations** - Don't run system with missing assets
2. **Restore from backup** - Use most recent backup with assets
3. **Verify integrity** - Test all documentation renders properly
4. **Update backups** - Ensure future backups include assets

## 🎯 Compliance Checklist

### **New Documentation Checklist:**
- [ ] Logo placed at very top of document
- [ ] Correct relative path used for file location
- [ ] Alt text reads exactly "Jaeger Logo"
- [ ] Blank line separates logo from title
- [ ] Document renders properly with logo visible

### **System Maintenance Checklist:**
- [ ] Branding assets present in `/branding` directory
- [ ] All documentation files include logo
- [ ] Backup systems include branding assets
- [ ] No broken logo references in any documentation

## 🚀 Implementation

### **For New Files:**
Always start new documentation with:
```markdown
![Jaeger Logo](../branding/jaeger-logo.png)

# Your Document Title
```

### **For Existing Files:**
Add logo to top if missing:
```markdown
![Jaeger Logo](../branding/jaeger-logo.png)

# Existing Document Title
```

## 📞 Support

### **Asset Issues:**
If you encounter problems with branding assets:
- Check file paths are correct for your document location
- Verify assets exist in `/branding` directory
- Test documentation rendering
- Restore from backup if assets are missing

### **Branding Questions:**
- Follow this guide exactly for consistency
- All documentation must include the logo
- Never modify or move branding assets
- Always include assets in backups

---

**🎨 Consistent branding ensures professional presentation and system integrity across the entire Jaeger ecosystem.**
