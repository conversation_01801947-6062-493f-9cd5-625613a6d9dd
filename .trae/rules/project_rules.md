# Core Principles & Unbreakable Rules
- <PERSON><PERSON><PERSON> FALLBACKS PRINCIPLE: System must fail fast and loud rather than use defaults or alternatives. Better to not trade than trade incorrectly.
- REAL DATA ONLY RULE: Never use synthetic data in tests - all tests must use real market data from /tests/RealTestData folder.
- UNBREAKABLE RULE: If backtester has existing functionality, it must NEVER be manually coded elsewhere - always use the backtester's built-in features.
- UNBREAKABLE RULE: All calculations must be done by the backtester - if <PERSON><PERSON><PERSON> is calculating something that the backtester can handle, use the backtester's functionality instead.
- UNBREAKABLE RULE: Walk-forward testing must use all possible backtester implementation instead of manually coding separate ones.
- UNBREAKABLE RULE: Research loop iterations must NOT restart from scratch - should reuse timeframes and build upon previous iteration work, not regenerate everything.
- User considers 97-98% signal rejection rates unacceptable and a critical problem, not a normal outcome.
- User identified contradiction: LLM should determine stop losses (UNBREAKABLE RULE) but system was fixed to use 20+ pip realistic stops, questioning who actually controls stop loss decisions.

# System Architecture & Components
- Cortex (cortex.py): Central AI orchestrator that ONLY coordinates LLM communication, not backtesting execution.
- backtesting.py: Professional framework handling ALL data ingestion, processing, and timeframe generation.
- Key components: backtesting_rule_parser.py, mt4_hardcoded_converter.py, backtesting_walk_forward_validator.py.
- NO hardcoded parameters anywhere - all parameters must be in configuration files (config.py and jaeger_config.env).
- User prefers extracting the official backtesting.py library from llm_env into /src directory instead of pip-installed version.
- The backtester has built-in sessions named 'london', 'ny', 'asia' that should be used instead of manual implementations.
- Jaeger system architecture: Use backtester for initial testing, THEN walk-forward validation as additional pass, THEN report patterns and generate files based on final combined results.
- Jaeger system now implements two-stage validation: backtesting first, then walk-forward validation as additional pass, with final profitability only determined after both stages pass.
- UNBREAKABLE RULE COMPLIANCE: Walk-forward validator now uses existing backtester implementation instead of manually coding separate backtesting logic - uses same PatternStrategy classes and backtesting framework.

# Trading Strategy & Analysis Approach
- Jaeger system now focuses specifically on Opening Range Breakout (ORB) patterns only.
- LLM must discover sophisticated multi-timeframe patterns that include behavioral context with breakout conditions.
- System finds both LONG and SHORT trades with realistic spread (1 pip) but no commission costs in backtesting.
- Initial account balance of $100,000 for backtesting configuration.
- Jaeger backtesting system uses 1:100 leverage by default and calculates position sizes in lots, not percentage-based sizing.
- Jaeger is designed for CFD trading with 1:100 leverage, which is critical for position sizing and margin calculations.
- ORB breakouts can be based on any candle of the session (1st, 2nd, 3rd, etc.) with flexibility throughout the entire pipeline.
- User expects ORB (Opening Range Breakout) strategies to be less restrictive and generate more trading signals than the current AI-generated patterns are producing.
- User prefers limit orders for ORB breakout entries instead of market orders for more realistic execution.
- User prefers LLM to determine optimal stop loss and take profit levels rather than using hardcoded values.
- LLM should determine stop losses using candle highs/lows, trailing stops, price percentages, or point values - not just risk-reward ratios - and should identify optimal entries/exits through repeatable pattern analysis.
- User expects the LLM system to automatically handle profitability optimization rather than requiring manual parameter adjustments.
- User prefers ORB patterns to allow breakouts at any candle throughout the entire trading session (not just first 10-15 candles), specifically mentioning London/Frankfurt sessions are equivalent.
- Walk forward testing must happen before pattern profitability is reported in the Jaeger system.
- Research loop iterations should IMPROVE existing patterns based on backtest/walkforward results, NOT create completely new patterns from scratch, iterating improvements until target requirements met.
- CRITICAL FIX IMPLEMENTED: Implemented true iterative pattern improvement architecture - system now discovers patterns once then improves them based on performance feedback instead of generating new patterns each iteration, transforming it from random pattern generator to intelligent research system.
- CRITICAL FIX IMPLEMENTED: Full Loop Automation now uses iterative pattern improvement instead of generating new patterns each iteration - patterns are discovered once then improved based on performance feedback until success criteria are met.
- CLEANUP COMPLETED: Successfully cleaned up all remnants of old wrong implementation, updated all documentation, and verified end-to-end functionality - the iterative improvement architecture is now fully operational and production-ready.

# LLM & Context Management
- User prefers dynamic context length detection that queries LM Studio for actual model capabilities.
- LLM context length should be set to 32000 tokens with fallback to maximum supported when 64k unavailable.
- System loads last 100 analysis sessions from /llm_data/SYMBOL/ directory for LLM learning and pattern improvement.
- User prefers LLM prompts to be edited so that LLM chooses one optimal timeframe for backtesting instead of offering multiple options.
- User prefers full loop automation where LLM generates strategy code, executes backtests, analyzes results, and iterates until success criteria are met.
- User considers full loop automation a huge upgrade that completes Jaeger and emphasizes the importance of properly updating documentation for major features.
- User has a FULL_LOOP_AUTOMATION_PROPOSAL.md document with improvement ideas that may need adaptation to current project state and wants feasibility assessment.
- User prefers lighter LLM settings as current configuration is causing performance issues on their computer.
- User prefers balanced performance optimizations rather than aggressive reductions that might compromise functionality.
- User prefers full loop automation to be enabled by default rather than opt-in after implementation is complete.
- Successfully simplified Jaeger's context management from complex 4-level hierarchy to simple sliding window (last 3 iterations), removed overkill features that could harm pattern discovery, and implemented clean 32K→64K context scaling - focusing on effectiveness over complexity.
- User concerned that sliding scale context management features may be overkill that harms pattern discovery effectiveness.
- User prefers sliding scale + summary-based context management for LM Studio over current fixed context approach, as long as LM Studio handles all context operations.
- User prefers context management documentation to be the primary file without 'simplified' qualifier - should be treated as the standard approach, not an alternative.

# Quality Standards & Development Practices
- Project uses JSON-based parsing with validation instead of text-based parsing as preferred refactoring approach.
- User strongly prefers simple, direct solutions without overengineering, wrapper classes, or unnecessary complexity.
- All modules must achieve 90%+ test coverage with all tests passing and warnings fixed.
- Comprehensive project cleanup required: remove dead code, unused imports/classes, duplicates, redundancies, etc.
- 100% consistent capitalization of Open, High, Low, Close columns across the entire project.
- User prefers systematic process of elimination debugging approach: test backtester first with simple strategy, then work backwards.
- Fact checker should be used in file generation for .md files in results folder to prevent LLM hallucinations, not in Stage 2 JSON validation.
- User prefers complete implementation of features with comprehensive documentation updates and full end-to-end testing validation.
- User prefers thorough end-to-end testing that allows processes to complete fully rather than killing them prematurely before changes are actually validated in practice.

# Session Hours & Critical Issues
- Trading session hours in UTC+1: Tokyo 1:00-7:00, London 9:00-17:30, New York 15:30-22:00, Sydney 23:00-5:00, Frankfurt 9:00-17:30, Hong Kong 2:00-8:00.
- Fixed critical session timezone bug: session filter was using GMT times instead of UTC+1 data timezone, causing Pattern 2 (Asian Gap) to generate 0 trades.
- CSV data files already have daylight savings factored in and use UTCPlus01 timezone as shown in filename.
- The Jaeger backtester has had issues successfully executing trades - this is a fundamental system issue that needs investigation.

# Output & MT4 EA Generation
- User prefers clean output without LLM sentences and JSON contents in run_jaeger.command.
- User wants separate MT4 EAs generated per profitable pattern instead of one EA containing all patterns.
- Jaeger output cleaned up: removed verbose LLM debug output and JSON previews, MT4 EA generation fixed to create individual EAs per profitable pattern, system now has professional console output.
- User prefers to remove verbose debug output including data column listings and neural handshake establishment messages for cleaner console output.
- User prefers to remove duplicate console output messages like 'FULL LOOP AUTOMATION ENABLED' and 'FULL LOOP AUTOMATION MODE ACTIVATED' that say the same thing twice.