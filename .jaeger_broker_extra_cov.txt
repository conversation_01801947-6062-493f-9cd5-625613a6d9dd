============================= test session starts ==============================
platform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/Jaeger
plugins: cov-6.2.1
collected 0 items / 1 error

==================================== ERRORS ====================================
___________ ERROR collecting tests/test_backtesting__broker_extra.py ___________
ImportError while importing test module '/Users/<USER>/Jaeger/tests/test_backtesting__broker_extra.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py:127: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_backtesting__broker_extra.py:5: in <module>
    from backtesting._broker import _Broker, _OutOfMoneyError
E   ModuleNotFoundError: No module named 'backtesting'
=========================== short test summary info ============================
ERROR tests/test_backtesting__broker_extra.py
!!!!!!!!!!!!!!!!!!!! Interrupted: 1 error during collection !!!!!!!!!!!!!!!!!!!!
=============================== 1 error in 0.50s ===============================
