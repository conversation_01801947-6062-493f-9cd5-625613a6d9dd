import unittest
import os
import pandas as pd
import numpy as np
import sys
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)
from backtesting._stats import dummy_stats, compute_stats

REAL_DATA_PATH = 'tests/RealTestData/dax_200_bars.csv'
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestBacktestingStatsJaeger(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real test data missing at ' + REAL_DATA_PATH)
        cls.df = pd.read_csv(REAL_DATA_PATH)
        if 'DateTime' in cls.df.columns:
            cls.df['DateTime'] = pd.to_datetime(cls.df['DateTime'])
            cls.df = cls.df.set_index('DateTime')
        for col in REQUIRED_COLS:
            if col not in cls.df.columns:
                raise AssertionError(f'UNBREAKABLE RULE VIOLATION: Missing required column: {col}')
        if cls.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')

    def test_dummy_stats(self):
        stats = dummy_stats()
        self.assertIn('Equity Final [$]', stats)
        # Vanilla backtesting.py dummy_stats creates 1 dummy trade, not 0
        self.assertEqual(stats['# Trades'], 1)
        self.assertTrue(isinstance(stats['Start'], pd.Timestamp))
        self.assertTrue(isinstance(stats['End'], pd.Timestamp))
        self.assertTrue(isinstance(stats['Duration'], pd.Timedelta))

    def test_compute_stats_no_trades(self):
        # Use real data, but no trades, flat equity
        equity = np.full(len(self.df), 100000.0)
        trades = []
        stats = compute_stats(trades, equity, self.df, strategy_instance=None)
        self.assertIn('Equity Final [$]', stats)
        self.assertEqual(stats['# Trades'], 0)
        self.assertEqual(stats['Start'], self.df.index[0])
        self.assertEqual(stats['End'], self.df.index[-1])
        self.assertEqual(stats['Duration'], self.df.index[-1] - self.df.index[0])
        self.assertTrue('_strategy' in stats)
        self.assertTrue('_equity_curve' in stats)
        self.assertTrue('_trades' in stats)
        self.assertTrue(isinstance(stats['_equity_curve'], pd.DataFrame))
        self.assertTrue(isinstance(stats['_trades'], pd.DataFrame))

    def test_compute_stats_with_realistic_trades(self):
        # Minimal fake trade objects using real data fields, strict OHLCV only
        class Trade:
            def __init__(self, size, entry_bar, exit_bar, entry_price, exit_price, entry_time, exit_time):
                self.size = size
                self.entry_bar = entry_bar
                self.exit_bar = exit_bar
                self.entry_price = entry_price
                self.exit_price = exit_price
                self.pl = (exit_price-entry_price) * size
                # pl_pct should be in decimal form (e.g. 0.008 for 0.8%), matching vanilla backtesting.py expectations
                self.pl_pct = (exit_price-entry_price)/entry_price
                self.entry_time = entry_time
                self.exit_time = exit_time
                self.sl = None  # Add stop loss attribute to match real Trade interface
                self.tp = None  # Add take profit attribute to match real Trade interface
                self.tag = None  # Add tag attribute to match real Trade interface
                self._commissions = 0.0  # Add commissions attribute to match real Trade interface
        # Use two real trades from real data
        t1 = Trade(1, 0, 1, float(self.df.iloc[0]['Open']), float(self.df.iloc[1]['Close']), self.df.index[0], self.df.index[1])
        t2 = Trade(2, 2, 3, float(self.df.iloc[2]['Open']), float(self.df.iloc[3]['Close']), self.df.index[2], self.df.index[3])
        trades = [t1, t2]
        equity = np.full(len(self.df), 100000.0)
        equity[1] += t1.pl
        equity[3] += t2.pl
        stats = compute_stats(trades, equity, self.df, strategy_instance=None)
        self.assertEqual(stats['# Trades'], 2)
        # Vanilla backtesting.py converts pl_pct to percentage for display, so compare with converted values
        self.assertAlmostEqual(stats['Best Trade [%]'], max(t1.pl_pct, t2.pl_pct) * 100)
        self.assertAlmostEqual(stats['Worst Trade [%]'], min(t1.pl_pct, t2.pl_pct) * 100)
        self.assertTrue(stats['Avg. Trade [%]'] != 0)
        self.assertTrue(stats['Win Rate [%]'] >= 0)
        self.assertTrue(isinstance(stats['Max. Trade Duration'], pd.Timedelta))
        self.assertTrue(isinstance(stats['Avg. Trade Duration'], pd.Timedelta))
        self.assertTrue(isinstance(stats['_trades'], pd.DataFrame))

if __name__ == '__main__':
    unittest.main()
