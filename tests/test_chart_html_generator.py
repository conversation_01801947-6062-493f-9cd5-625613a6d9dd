import unittest
import sys
import os
import pandas as pd
import numpy as np
import tempfile
import shutil
from unittest.mock import Mock, patch

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from chart_html_generator import HTMLChartGenerator

REAL_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv'))

class TestChartHTMLGenerator(unittest.TestCase):
    """Test cases for HTMLChartGenerator using real market data"""
    
    def setUp(self):
        """Set up test fixtures with real market data"""
        self.generator = HTMLChartGenerator()
        
        # Load real market data
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data not found at {}".format(REAL_DATA_PATH))
        
        self.real_data = pd.read_csv(REAL_DATA_PATH)
        
        # Validate real data structure
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            if col not in self.real_data.columns:
                raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
        
        # Ensure realistic price movements
        self.assertGreater(self.real_data['High'].max(), self.real_data['Low'].min())
        self.assertGreater(len(self.real_data), 50, "Insufficient real data for testing")
        
        # Create datetime index for real data
        self.real_data.index = pd.date_range(start='2023-01-01', periods=len(self.real_data), freq='1h')
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_instantiation(self):
        """Test HTMLChartGenerator instantiation"""
        try:
            gen = HTMLChartGenerator()
            self.assertIsNotNone(gen)
            self.assertIsNotNone(gen.logger)
        except Exception as e:
            self.fail(f"HTMLChartGenerator failed to instantiate: {e}")

    def test_generate_backtest_html_chart_success(self):
        """Test successful HTML chart generation with trades"""
        gen = HTMLChartGenerator()
        
        class DummyStats(dict):
            def __init__(self, real_data, *args, **kwargs):
                super().__init__(*args, **kwargs)
                # Create realistic trades using real data
                trades_data = {
                    'EntryTime': [real_data.index[10], real_data.index[50]],
                    'ExitTime': [real_data.index[20], real_data.index[60]],
                    'EntryPrice': [real_data['Close'].iloc[10], real_data['Close'].iloc[50]],
                    'ExitPrice': [real_data['Close'].iloc[20], real_data['Close'].iloc[60]]
                }
                self._trades = pd.DataFrame(trades_data)
                
                # Create realistic equity curve
                equity_data = {
                    'Equity': np.cumsum(np.random.randn(len(real_data)) * 100) + 100000
                }
                self._equity_curve = pd.DataFrame(equity_data, index=real_data.index)
            
            def get(self, key, default=None):
                return super().get(key, default)
        
        stats = DummyStats(self.real_data, {
            'Return [%]': 15.5,
            '# Trades': 2,
            'Win Rate [%]': 100.0,
            'Profit Factor': 2.0,
            'Max. Drawdown [%]': -5.5,
            'Sharpe Ratio': 1.5,
            'Start': str(self.real_data.index[0]),
            'End': str(self.real_data.index[-1]),
            'Equity Final [$]': 115500
        })
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'chart.html')
            result = gen.generate_backtest_html_chart(stats, self.real_data, 'DAX', output_path)
            self.assertTrue(os.path.exists(result))
            with open(result) as f:
                html = f.read()
                self.assertIn('html', html.lower())
                self.assertIn('DAX - Backtesting Results', html)
                self.assertIn('plotly', html)

    def test_generate_backtest_html_chart_no_trades(self):
        """Test HTML chart generation without trades"""
        gen = HTMLChartGenerator()
        
        class DummyStats(dict):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self._trades = pd.DataFrame()  # Empty trades
                self._equity_curve = pd.DataFrame()  # Empty equity curve
            
            def get(self, key, default=None):
                return super().get(key, default)
        
        stats = DummyStats({
            'Return [%]': 0.0,
            '# Trades': 0,
            'Win Rate [%]': 0.0,
            'Profit Factor': 0.0,
            'Max. Drawdown [%]': 0.0,
            'Sharpe Ratio': 0.0,
            'Start': '2023-01-01',
            'End': '2023-01-31',
            'Equity Final [$]': 100000
        })
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'no_trades.html')
            result = gen.generate_backtest_html_chart(stats, self.real_data, 'DAX', output_path)
            self.assertTrue(os.path.exists(result))
            with open(result) as f:
                html = f.read()
                self.assertIn('No trades executed', html)
                self.assertIn('Equity (No Trades)', html)

    def test_generate_backtest_html_chart_failfast_missing_col(self):
        """Test chart generation fails with missing required columns"""
        df = pd.read_csv(REAL_DATA_PATH)
        df = df.drop(columns=['Open'])
        gen = HTMLChartGenerator()
        
        class DummyStats(dict):
            def get(self, key, default=None):
                return super().get(key, default)
        
        stats = DummyStats({
            'Return [%]': 1.0,
            '# Trades': 1,
            'Win Rate [%]': 100.0,
            'Profit Factor': 2.0,
            'Max. Drawdown [%]': 0.5,
            'Sharpe Ratio': 1.5,
            'Start': '2020-01-01',
            'End': '2020-12-31'
        })
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'fail.html')
            result = gen.generate_backtest_html_chart(stats, df, 'DAX', output_path)
            self.assertIsNone(result)
            self.assertFalse(os.path.exists(output_path))
    
    def test_generate_backtest_html_chart_missing_attributes(self):
        """Test chart generation with missing stats attributes"""
        gen = HTMLChartGenerator()
        
        # Create mock stats without _trades and _equity_curve attributes
        stats = Mock()
        stats.get = Mock(side_effect=lambda key, default=None: {
            'Equity Final [$]': 100000
        }.get(key, default))
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'missing_attrs.html')
            result = gen.generate_backtest_html_chart(stats, self.real_data, 'DAX', output_path)
            self.assertTrue(os.path.exists(result))
    
    def test_format_performance_metrics(self):
        """Test performance metrics formatting"""
        gen = HTMLChartGenerator()
        
        mock_stats = Mock()
        mock_stats.get = Mock(side_effect=lambda key, default=None: {
            'Return [%]': 15.75,
            '# Trades': 25,
            'Win Rate [%]': 68.5,
            'Profit Factor': 2.1,
            'Max. Drawdown [%]': -12.3,
            'Sharpe Ratio': 1.45,
            'Start': '2023-01-01',
            'End': '2023-12-31'
        }.get(key, default))
        
        result = gen._format_performance_metrics(mock_stats)
        
        self.assertIn('Return: 15.75%', result)
        self.assertIn('Trades: 25', result)
        self.assertIn('Win Rate: 68.5%', result)
        self.assertIn('Profit Factor: 2.10', result)
        self.assertIn('Max Drawdown: -12.30%', result)
        self.assertIn('Sharpe Ratio: 1.45', result)
        self.assertIn('Start: 2023-01-01', result)
        self.assertIn('End: 2023-12-31', result)
    
    def test_format_performance_metrics_error_handling(self):
        """Test performance metrics formatting with errors"""
        gen = HTMLChartGenerator()
        
        # Create mock stats that raises exception
        mock_stats = Mock()
        mock_stats.get = Mock(side_effect=Exception("Test error"))
        
        result = gen._format_performance_metrics(mock_stats)
        
        self.assertIn('Metrics unavailable', result)
        self.assertIn('Test error', result)
    
    def test_generate_optimization_heatmap(self):
        """Test optimization heatmap generation"""
        gen = HTMLChartGenerator()
        
        # Create mock optimization results
        mock_results = pd.DataFrame({
            'param1': [1, 2, 3],
            'param2': [4, 5, 6],
            'return': [0.1, 0.2, 0.15]
        })
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'heatmap.html')
            result = gen.generate_optimization_heatmap(mock_results, output_path)
            
            self.assertEqual(result, output_path)
            self.assertTrue(os.path.exists(output_path))
            
            # Verify HTML content
            with open(output_path, 'r') as f:
                html_content = f.read()
                self.assertIn('Parameter Optimization Heatmap', html_content)
                self.assertIn('plotly', html_content)
    
    def test_generate_optimization_heatmap_empty_results(self):
        """Test optimization heatmap with empty results"""
        gen = HTMLChartGenerator()
        empty_results = pd.DataFrame()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'empty_heatmap.html')
            result = gen.generate_optimization_heatmap(empty_results, output_path)
            self.assertIsNone(result)
    
    def test_generate_optimization_heatmap_none_results(self):
        """Test optimization heatmap with None results"""
        gen = HTMLChartGenerator()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'none_heatmap.html')
            result = gen.generate_optimization_heatmap(None, output_path)
            self.assertIsNone(result)
    
    def test_output_directory_creation(self):
        """Test automatic output directory creation"""
        gen = HTMLChartGenerator()
        
        # Create nested directory path that doesn't exist
        nested_path = os.path.join(self.temp_dir, 'nested', 'deep', 'path', 'chart.html')
        
        class DummyStats(dict):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self._trades = pd.DataFrame()
                self._equity_curve = pd.DataFrame()
            
            def get(self, key, default=None):
                return super().get(key, default)
        
        stats = DummyStats({'Equity Final [$]': 100000})
        
        result = gen.generate_backtest_html_chart(stats, self.real_data, 'DAX', nested_path)
        
        self.assertEqual(result, nested_path)
        self.assertTrue(os.path.exists(nested_path))
        self.assertTrue(os.path.exists(os.path.dirname(nested_path)))
    
    def test_chart_with_error_handling(self):
        """Test chart generation error handling"""
        gen = HTMLChartGenerator()
        
        # Test with invalid data that should cause an error
        invalid_data = pd.DataFrame({'invalid': [1, 2, 3]})
        mock_stats = Mock()
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'error.html')
            result = gen.generate_backtest_html_chart(mock_stats, invalid_data, 'DAX', output_path)
            self.assertIsNone(result)
    
    def test_real_data_validation(self):
        """Test that real market data meets quality requirements"""
        # Verify data quality
        self.assertGreater(len(self.real_data), 100, "Need sufficient data for comprehensive testing")
        
        # Check for realistic price relationships
        for i in range(min(100, len(self.real_data))):  # Check first 100 rows for performance
            row = self.real_data.iloc[i]
            self.assertLessEqual(row['Low'], row['High'], f"Low > High at index {i}")
            self.assertLessEqual(row['Low'], row['Open'], f"Low > Open at index {i}")
            self.assertLessEqual(row['Low'], row['Close'], f"Low > Close at index {i}")
            self.assertGreaterEqual(row['High'], row['Open'], f"High < Open at index {i}")
            self.assertGreaterEqual(row['High'], row['Close'], f"High < Close at index {i}")
        
        # Check for realistic price movements (no extreme jumps)
        price_changes = self.real_data['Close'].pct_change().dropna()
        max_change = price_changes.abs().max()
        self.assertLess(max_change, 0.5, "Unrealistic price movements detected")

if __name__ == '__main__':
    unittest.main()
