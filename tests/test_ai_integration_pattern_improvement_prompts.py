#!/usr/bin/env python3
"""
Test suite for Pattern Improvement Prompts module
Tests the LLM prompt generation for pattern improvement functionality
"""

import unittest
import os
import sys

# Add src directory to path for proper imports
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from ai_integration.pattern_improvement_prompts import PatternImprovementPrompts


class TestPatternImprovementPrompts(unittest.TestCase):
    """Test cases for PatternImprovementPrompts class"""

    def setUp(self):
        """Setup test fixtures"""
        self.prompts = PatternImprovementPrompts()
        
        # Sample test data
        self.sample_patterns = [
            {
                "pattern_name": "ORB London Breakout",
                "entry_conditions": [
                    {"condition": "orb_breakout_above", "session": "london"}
                ],
                "exit_conditions": [
                    {"condition": "trailing_stop_candle_low", "lookback": 2}
                ]
            }
        ]
        
        self.sample_performance_feedback = {
            "pattern_performance": [
                {
                    "pattern_number": 1,
                    "pattern_name": "ORB London Breakout",
                    "is_profitable": False,
                    "metrics": {
                        "win_rate": 0.45,
                        "sharpe_ratio": -0.5,
                        "profit_factor": 0.8,
                        "max_drawdown": -0.15,
                        "trade_count": 20,
                        "total_return": -0.025
                    }
                }
            ],
            "improvement_suggestions": [
                "Consider tighter stop losses",
                "Add volume filter for better entries",
                "Improve exit timing"
            ],
            "summary": "Pattern shows potential but needs refinement in risk management",
            "iteration": 2
        }

    def test_class_instantiation(self):
        """Test that PatternImprovementPrompts can be instantiated"""
        prompts = PatternImprovementPrompts()
        self.assertIsInstance(prompts, PatternImprovementPrompts)

    def test_get_schema_constraints(self):
        """Test schema constraints retrieval"""
        constraints = PatternImprovementPrompts.get_schema_constraints()
        
        # Verify it returns a dictionary
        self.assertIsInstance(constraints, dict)
        
        # Verify required keys exist
        required_keys = ["entry_conditions", "exit_conditions", "entry_logic_options", "sessions", "timeframe_options"]
        for key in required_keys:
            self.assertIn(key, constraints)
        
        # Verify entry conditions include ORB-specific conditions
        entry_conditions = constraints["entry_conditions"]
        self.assertIn("orb_breakout_above", entry_conditions)
        self.assertIn("orb_breakout_below", entry_conditions)
        self.assertIn("opening_range_high", entry_conditions)
        self.assertIn("opening_range_low", entry_conditions)
        
        # Verify exit conditions include trailing stops
        exit_conditions = constraints["exit_conditions"]
        self.assertIn("trailing_stop_candle_low", exit_conditions)
        self.assertIn("trailing_stop_candle_high", exit_conditions)
        
        # Verify sessions include expected trading sessions
        sessions = constraints["sessions"]
        self.assertIn("london", sessions)
        self.assertIn("ny", sessions)
        self.assertIn("asian", sessions)

    def test_generate_improvement_prompt_basic(self):
        """Test basic improvement prompt generation"""
        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=self.sample_patterns,
            performance_feedback=self.sample_performance_feedback,
            symbol="GBPUSD"
        )
        
        # Verify prompt is generated
        self.assertIsInstance(prompt, str)
        self.assertGreater(len(prompt), 100)  # Should be substantial
        
        # Verify key elements are included
        self.assertIn("GBPUSD", prompt)
        self.assertIn("ORB London Breakout", prompt)
        self.assertIn("improvement", prompt.lower())

    def test_generate_improvement_prompt_no_symbol(self):
        """Test improvement prompt generation without symbol"""
        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=self.sample_patterns,
            performance_feedback=self.sample_performance_feedback
        )
        
        # Should still generate a valid prompt
        self.assertIsInstance(prompt, str)
        self.assertGreater(len(prompt), 100)

    def test_generate_improvement_prompt_empty_patterns(self):
        """Test improvement prompt generation with empty patterns"""
        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=[],
            performance_feedback=self.sample_performance_feedback
        )
        
        # Should handle empty patterns gracefully
        self.assertIsInstance(prompt, str)

    def test_generate_improvement_prompt_empty_feedback(self):
        """Test improvement prompt generation with empty feedback"""
        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=self.sample_patterns,
            performance_feedback={}
        )
        
        # Should handle empty feedback gracefully
        self.assertIsInstance(prompt, str)

    def test_generate_improvement_prompt_missing_feedback_keys(self):
        """Test improvement prompt generation with missing feedback keys"""
        incomplete_feedback = {
            "summary": "Basic summary only"
        }
        
        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=self.sample_patterns,
            performance_feedback=incomplete_feedback
        )
        
        # Should handle missing keys gracefully
        self.assertIsInstance(prompt, str)
        self.assertIn("Basic summary only", prompt)

    def test_schema_constraints_immutability(self):
        """Test that schema constraints are consistent across calls"""
        constraints1 = PatternImprovementPrompts.get_schema_constraints()
        constraints2 = PatternImprovementPrompts.get_schema_constraints()
        
        # Should return the same structure
        self.assertEqual(constraints1, constraints2)

    def test_entry_conditions_completeness(self):
        """Test that entry conditions cover all ORB scenarios"""
        constraints = PatternImprovementPrompts.get_schema_constraints()
        entry_conditions = constraints["entry_conditions"]
        
        # Should include both breakout directions
        self.assertIn("orb_breakout_above", entry_conditions)
        self.assertIn("orb_breakout_below", entry_conditions)
        
        # Should include range boundaries
        self.assertIn("opening_range_high", entry_conditions)
        self.assertIn("opening_range_low", entry_conditions)
        
        # Should include session and time filters
        self.assertIn("session_filter", entry_conditions)
        self.assertIn("orb_time_filter", entry_conditions)

    def test_exit_conditions_completeness(self):
        """Test that exit conditions cover risk management scenarios"""
        constraints = PatternImprovementPrompts.get_schema_constraints()
        exit_conditions = constraints["exit_conditions"]
        
        # Should include trailing stops
        self.assertIn("trailing_stop_candle_low", exit_conditions)
        self.assertIn("trailing_stop_candle_high", exit_conditions)
        
        # Should include fixed stops and targets
        self.assertIn("fixed_pips_stop", exit_conditions)
        self.assertIn("fixed_pips_target", exit_conditions)
        
        # Should include pattern failure exits
        self.assertIn("orb_pattern_failure", exit_conditions)
        self.assertIn("session_end_exit", exit_conditions)

    def test_prompt_contains_schema_reference(self):
        """Test that generated prompts reference the schema constraints"""
        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=self.sample_patterns,
            performance_feedback=self.sample_performance_feedback
        )
        
        # Should reference JSON schema or structure
        self.assertTrue(
            "json" in prompt.lower() or "schema" in prompt.lower() or "structure" in prompt.lower(),
            "Prompt should reference JSON schema or structure"
        )

    def test_no_hardcoded_params(self):
        """Test that no hardcoded parameters are used"""
        # This test ensures the module follows the project's no-hardcoded-params rule
        constraints = PatternImprovementPrompts.get_schema_constraints()

        # Verify that constraints are defined as data structures, not hardcoded values
        self.assertIsInstance(constraints["entry_conditions"], list)
        self.assertIsInstance(constraints["exit_conditions"], list)
        self.assertIsInstance(constraints["sessions"], list)
        self.assertIsInstance(constraints["timeframe_options"], list)

    def test_generate_improvement_prompt_with_failure_reasons(self):
        """Test improvement prompt generation with failure reasons"""
        feedback_with_failures = {
            "pattern_performance": [
                {
                    "pattern_number": 1,
                    "pattern_name": "ORB London Breakout",
                    "is_profitable": False,
                    "failure_reasons": ["High false breakouts", "Poor exit timing", "Inadequate stop loss"],
                    "metrics": {
                        "win_rate": 0.35,
                        "sharpe_ratio": -0.8,
                        "profit_factor": 0.6,
                        "max_drawdown": -0.25,
                        "trade_count": 15,
                        "total_return": -0.05
                    }
                }
            ],
            "improvement_suggestions": ["Improve entry filters"],
            "summary": "Pattern needs significant improvement",
            "iteration": 3
        }

        prompt = PatternImprovementPrompts.generate_improvement_prompt(
            current_patterns=self.sample_patterns,
            performance_feedback=feedback_with_failures
        )

        # Verify failure reasons are included
        self.assertIn("High false breakouts", prompt)
        self.assertIn("Poor exit timing", prompt)
        self.assertIn("Inadequate stop loss", prompt)
        self.assertIn("Failure Reasons:", prompt)


if __name__ == '__main__':
    unittest.main()
