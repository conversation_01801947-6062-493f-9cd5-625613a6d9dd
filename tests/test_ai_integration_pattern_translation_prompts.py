import unittest
import os
import sys
import json
from unittest.mock import patch, Mock

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from ai_integration.pattern_translation_prompts import PatternTranslationPrompts, get_session_for_symbol

class TestPatternTranslationPrompts(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures"""
        self.prompts = PatternTranslationPrompts()
        
    def test_class_instantiation(self):
        """Test basic class instantiation"""
        try:
            prompts = PatternTranslationPrompts()
            self.assertIsInstance(prompts, PatternTranslationPrompts)
        except Exception as e:
            self.fail(f"PatternTranslationPrompts failed to instantiate: {e}")

    def test_no_hardcoded_params(self):
        """Test that no hardcoded parameters exist in the module"""
        path = os.path.join(os.path.dirname(__file__), '../src/ai_integration/pattern_translation_prompts.py')
        with open(path, 'r') as f:
            content = f.read()
        # Allow some specific hardcoded values that are configuration constants
        allowed_hardcoded = ['="london"', '="ny"', '="all"', '="AND"', '="OR"']
        lines_with_hardcoded = []
        for line_num, line in enumerate(content.split('\n'), 1):
            if '="' in line and not any(allowed in line for allowed in allowed_hardcoded):
                lines_with_hardcoded.append(f"Line {line_num}: {line.strip()}")
        
        if lines_with_hardcoded:
            self.fail(f'UNBREAKABLE RULE VIOLATION: Hardcoded parameters found:\n' + '\n'.join(lines_with_hardcoded))
            
    def test_get_session_for_symbol_european_indices(self):
        """Test session determination for European indices"""
        # FTSE related symbols
        self.assertEqual(get_session_for_symbol("FTSE"), "london")
        self.assertEqual(get_session_for_symbol("GBRIDXGBP"), "london")
        self.assertEqual(get_session_for_symbol("UK100"), "london")
        
        # DAX related symbols
        self.assertEqual(get_session_for_symbol("DAX"), "london")
        self.assertEqual(get_session_for_symbol("DEUIDXEUR"), "london")
        self.assertEqual(get_session_for_symbol("GER40"), "london")
        
    def test_get_session_for_symbol_us_indices(self):
        """Test session determination for US indices"""
        self.assertEqual(get_session_for_symbol("DOW"), "ny")
        self.assertEqual(get_session_for_symbol("NASDAQ"), "ny")
        self.assertEqual(get_session_for_symbol("US30"), "ny")
        self.assertEqual(get_session_for_symbol("NAS100"), "ny")
        self.assertEqual(get_session_for_symbol("DJI"), "ny")
        
    def test_get_session_for_symbol_forex_pairs(self):
        """Test session determination for forex pairs"""
        self.assertEqual(get_session_for_symbol("EURUSD"), "all")
        self.assertEqual(get_session_for_symbol("GBPJPY"), "all")
        self.assertEqual(get_session_for_symbol("USDCAD"), "all")
        self.assertEqual(get_session_for_symbol("AUDUSD"), "all")

    def test_get_session_for_symbol_short_currency_symbols(self):
        """Test session determination for short currency symbols (line 46 coverage)"""
        # Test short symbols with currency codes (less than 6 characters)
        self.assertEqual(get_session_for_symbol("USD"), "all")
        self.assertEqual(get_session_for_symbol("EUR"), "all")
        self.assertEqual(get_session_for_symbol("GBP"), "all")
        self.assertEqual(get_session_for_symbol("JPY"), "all")
        
    def test_get_session_for_symbol_unknown_instruments(self):
        """Test session determination for unknown instruments"""
        self.assertEqual(get_session_for_symbol("UNKNOWN"), "all")
        self.assertEqual(get_session_for_symbol("XYZ123"), "all")
        self.assertEqual(get_session_for_symbol(""), "all")
        
    def test_get_session_for_symbol_case_insensitive(self):
        """Test that session determination is case insensitive"""
        self.assertEqual(get_session_for_symbol("dax"), "london")
        self.assertEqual(get_session_for_symbol("dow"), "ny")
        self.assertEqual(get_session_for_symbol("eurusd"), "all")
        
    def test_get_backtesting_constraints(self):
        """Test backtesting constraints structure"""
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        
        # Check required keys exist
        required_keys = ['entry_conditions', 'exit_conditions', 'entry_logic_options', 
                        'position_sizing_methods', 'timeframe_options', 'market_regimes', 'sessions']
        for key in required_keys:
            self.assertIn(key, constraints)
            self.assertIsInstance(constraints[key], list)
            self.assertGreater(len(constraints[key]), 0)
            
        # Check specific ORB-related conditions
        self.assertIn('orb_breakout_above', constraints['entry_conditions'])
        self.assertIn('orb_breakout_below', constraints['entry_conditions'])
        self.assertIn('session_filter', constraints['entry_conditions'])
        
        # Check exit conditions
        self.assertIn('trailing_stop_candle_low', constraints['exit_conditions'])
        self.assertIn('fixed_pips_stop', constraints['exit_conditions'])
        self.assertIn('orb_pattern_failure', constraints['exit_conditions'])
        
    def test_get_translation_principles(self):
        """Test translation principles"""
        principles = PatternTranslationPrompts.get_translation_principles()
        
        self.assertIsInstance(principles, list)
        self.assertGreater(len(principles), 0)
        
        # Check that principles focus on profitability
        principles_text = ' '.join(principles).upper()
        self.assertIn('PROFIT', principles_text)
        self.assertIn('CFD', principles_text)
        
    def test_get_translation_examples(self):
        """Test translation examples structure"""
        examples = PatternTranslationPrompts.get_translation_examples()
        
        self.assertIsInstance(examples, list)
        self.assertGreater(len(examples), 0)
        
        for example in examples:
            # Check required keys
            self.assertIn('sophisticated', example)
            self.assertIn('json_output', example)
            
            json_output = example['json_output']
            # Check required JSON structure
            required_json_keys = ['pattern_name', 'description', 'entry_conditions', 'exit_conditions']
            for key in required_json_keys:
                self.assertIn(key, json_output)
                
        # Verify examples are valid JSON when serialized
        for example in examples:
            try:
                json.dumps(example['json_output'])
            except (TypeError, ValueError) as e:
                self.fail(f"Example JSON output is not serializable: {e}")
                
    def test_generate_stage2_translation_prompt_basic(self):
        """Test basic prompt generation"""
        sophisticated_patterns = "Test pattern for London ORB breakout"
        
        prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(sophisticated_patterns)
        
        self.assertIsInstance(prompt, str)
        self.assertGreater(len(prompt), 1000)  # Should be a substantial prompt
        self.assertIn(sophisticated_patterns, prompt)
        self.assertIn('STAGE 2', prompt)
        self.assertIn('JSON', prompt)
        
    def test_generate_stage2_translation_prompt_with_symbol(self):
        """Test prompt generation with specific symbol"""
        sophisticated_patterns = "Test pattern"
        symbol = "DAX"
        
        prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(sophisticated_patterns, symbol)
        
        self.assertIn(symbol, prompt)
        self.assertIn('london', prompt)  # DAX should use London session
        self.assertIn('session_filter', prompt)
        
    def test_generate_stage2_translation_prompt_forex_symbol(self):
        """Test prompt generation with forex symbol"""
        sophisticated_patterns = "Test pattern"
        symbol = "EURUSD"
        
        prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(sophisticated_patterns, symbol)
        
        self.assertIn(symbol, prompt)
        self.assertIn('"all"', prompt)  # Forex should use all sessions
        
    def test_generate_stage2_translation_prompt_contains_constraints(self):
        """Test that prompt contains all necessary constraints"""
        sophisticated_patterns = "Test pattern"
        
        prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(sophisticated_patterns)
        
        # Check for key constraint sections
        self.assertIn('orb_breakout_above', prompt)
        self.assertIn('orb_breakout_below', prompt)
        self.assertIn('fixed_pips_stop', prompt)
        self.assertIn('trailing_stop_candle_low', prompt)
        self.assertIn('position_sizing', prompt)
        self.assertIn('CFD', prompt)
        self.assertIn('1:100', prompt)  # Leverage information
        
    def test_generate_stage2_translation_prompt_contains_examples(self):
        """Test that prompt contains translation examples"""
        sophisticated_patterns = "Test pattern"
        
        prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(sophisticated_patterns)
        
        # Should contain example JSON structures
        self.assertIn('London 30-Minute ORB Breakout', prompt)
        self.assertIn('NY 60-Minute ORB Breakdown', prompt)
        self.assertIn('json', prompt.lower())
        
    def test_validate_translation_output_valid_entry(self):
        """Test validation with valid entry conditions"""
        output = "This pattern uses orb_breakout_above condition"
        
        result = PatternTranslationPrompts.validate_translation_output(output)
        
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['errors']), 0)
        
    def test_validate_translation_output_valid_exit(self):
        """Test validation with valid exit conditions"""
        output = "This pattern uses fixed_pips_stop for exit"
        
        result = PatternTranslationPrompts.validate_translation_output(output)
        
        self.assertTrue(result['valid'])
        
    def test_validate_translation_output_valid_position_sizing(self):
        """Test validation with valid position sizing"""
        output = "This pattern uses fixed_percent position sizing"
        
        result = PatternTranslationPrompts.validate_translation_output(output)
        
        self.assertTrue(result['valid'])
        
    def test_validate_translation_output_missing_conditions(self):
        """Test validation with missing conditions"""
        output = "This is a basic pattern without specific conditions"
        
        result = PatternTranslationPrompts.validate_translation_output(output)
        
        self.assertTrue(result['valid'])  # Still valid but with warnings
        self.assertGreater(len(result['warnings']), 0)
        
    def test_validate_translation_output_comprehensive(self):
        """Test validation with comprehensive output"""
        output = """
        This pattern uses orb_breakout_above for entry,
        fixed_pips_stop for exit, and fixed_percent for position sizing
        """
        
        result = PatternTranslationPrompts.validate_translation_output(output)
        
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['warnings']), 0)

if __name__ == '__main__':
    unittest.main()
