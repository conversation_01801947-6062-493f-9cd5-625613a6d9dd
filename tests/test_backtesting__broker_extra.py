import unittest
import os
import pandas as pd
import numpy as np
import sys, os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from backtesting.backtesting import _Broker, _OutOfMoneyError
from backtesting._util import _Data

REAL_DATA_PATH = 'tests/RealTestData/dax_200_bars.csv'
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestBrokerExtraCoverage(unittest.TestCase):
    def setUp(self):
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real test data missing at ' + REAL_DATA_PATH)
        self.df = pd.read_csv(REAL_DATA_PATH)
        if 'DateTime' in self.df.columns:
            self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])
            self.df = self.df.set_index('DateTime')
        for col in REQUIRED_COLS:
            if col not in self.df.columns:
                raise AssertionError(f'UNBREAKABLE RULE VIOLATION: Missing required column: {col}')
        if self.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')
        self.data = _Data(self.df)
        self.index = self.df.index

    def make_broker(self, **kwargs):
        return _Broker(
            data=self.data,
            cash=kwargs.get('cash', 100000),
            spread=kwargs.get('spread', 0.0),
            commission=kwargs.get('commission', 0.0),
            margin=kwargs.get('margin', 0.01),
            trade_on_close=kwargs.get('trade_on_close', True),
            hedging=kwargs.get('hedging', False),
            exclusive_orders=kwargs.get('exclusive_orders', False),
            index=self.index
        )

    def test_commission_callable(self):
        # Cover branch: commission as callable
        def custom_comm(size, price):
            return 42.0
        broker = self.make_broker(commission=custom_comm)
        self.assertEqual(broker._commission(10, 100), 42.0)

    def test_out_of_money_error(self):
        # Cover _OutOfMoneyError and equity <= 0 branch
        broker = self.make_broker(cash=1)
        # Place an order that will drain account
        broker.new_order(1)
        broker._i = len(self.data) - 1
        # Force equity <= 0
        broker._cash = -1
        with self.assertRaises(_OutOfMoneyError):
            broker.next()

    def test_exclusive_orders(self):
        # Cover exclusive_orders logic
        broker = self.make_broker(exclusive_orders=True)
        o1 = broker.new_order(1)
        o2 = broker.new_order(1)
        self.assertEqual(len(broker.orders), 1)  # Only last order remains due to exclusive logic
        broker._i = len(self.data) - 1
        broker._process_orders()
        # Only last order should result in a trade
        self.assertLessEqual(len(broker.trades), 1)

    def test_relative_size_order_and_margin_warning(self):
        # Cover branch: relative size order and insufficient margin
        broker = self.make_broker()
        broker.new_order(0.000001)  # Very small relative size, should warn and be cancelled
        broker._i = len(self.data) - 1
        with self.assertWarns(UserWarning):
            broker._process_orders()
        self.assertEqual(len(broker.trades), 0)

    def test_long_order_sl_limit_tp_logic(self):
        broker = self.make_broker()
        # SL > TP for long order should raise ValueError
        with self.assertRaises(ValueError):
            broker.new_order(1, sl=20000, tp=18000)

    def test_short_order_tp_limit_sl_logic(self):
        broker = self.make_broker()
        # TP > SL for short order should raise ValueError
        with self.assertRaises(ValueError):
            broker.new_order(-1, sl=18000, tp=20000)

    def test_exclusive_orders_cancel_orders(self):
        broker = self.make_broker(exclusive_orders=True)
        # Place a non-contingent order
        o1 = broker.new_order(1)
        # Place a second order, should trigger cancel on first
        o2 = broker.new_order(1)
        self.assertEqual(len(broker.orders), 1)  # Only last order remains

    def test_stop_and_limit_same_bar_pessimistic(self):
        broker = self.make_broker()
        idx = len(self.df) - 2
        open_ = self.df['Open'].iloc[idx]
        high = self.df['High'].iloc[idx]
        low = self.df['Low'].iloc[idx]
        limit = (open_ + low) / 2
        stop = (open_ + high) / 2
        if limit > stop:
            limit, stop = stop, limit
        broker._i = idx
        order = broker.new_order(1, limit=limit, stop=stop)
        broker._process_orders()
        self.assertIn(order, broker.orders)
        self.assertEqual(len(broker.trades), 0)

    def test_limit_order_not_hit(self):
        broker = self.make_broker()
        limit_price = self.df['Low'].min() - 1000
        order = broker.new_order(-1, limit=limit_price)
        broker._i = len(self.data) - 1
        broker._process_orders()
        self.assertNotIn(order, broker.orders)
        self.assertEqual(len(broker.trades), 1)

    def test_stop_order_not_hit(self):
        broker = self.make_broker()
        stop_price = self.df['High'].max() + 1000
        order = broker.new_order(1, stop=stop_price)
        broker._i = len(self.data) - 1
        broker._process_orders()
        self.assertIn(order, broker.orders)
        self.assertEqual(len(broker.trades), 0)

    def test_contingent_order_closes_trade(self):
        broker = self.make_broker()
        order = broker.new_order(1)
        broker._i = len(self.data) - 1
        broker._process_orders()
        self.assertEqual(len(broker.trades), 1)
        trade = broker.trades[0]
        # Place a contingent SL order (parent_trade)
        sl_order = broker.new_order(-1, trade=trade)
        broker._i = len(self.data) - 1
        broker._process_orders()
        # Trade should be closed
        self.assertEqual(len(broker.trades), 0)

    def test_fifo_closing_opposite_trade(self):
        broker = self.make_broker()
        # Open a long trade
        broker.new_order(1)
        broker._i = len(self.data) - 1
        broker._process_orders()
        self.assertEqual(len(broker.trades), 1)
        # Place a short order of equal size, should close the long trade
        broker.new_order(-1)
        broker._i = len(self.data) - 1
        broker._process_orders()
        self.assertEqual(len(broker.trades), 0)
        self.assertGreaterEqual(len(broker.closed_trades), 1)

    def test_partial_reduce_existing_trade(self):
        broker = self.make_broker()
        broker.new_order(2)
        broker._i = len(self.data) - 1
        broker._process_orders()
        self.assertEqual(len(broker.trades), 1)
        # Place an opposite order of smaller size, should partially reduce
        broker.new_order(-1)
        broker._i = len(self.data) - 1
        broker._process_orders()
        # One trade should remain, but size should be reduced
        self.assertEqual(len(broker.trades), 1)
        self.assertGreaterEqual(len(broker.closed_trades), 1)

    def test_hedging_branch(self):
        # Cover branch: _hedging True disables closing opposite trades
        broker = self.make_broker(hedging=True)
        o1 = broker.new_order(1)
        o2 = broker.new_order(-1)
        broker._i = len(self.data) - 1
        broker._process_orders()
        # Both trades should exist if hedging is enabled
        self.assertGreaterEqual(len(broker.trades), 1)

if __name__ == '__main__':
    unittest.main()
