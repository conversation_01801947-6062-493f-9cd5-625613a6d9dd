import os
import sys
import pytest
import pandas as pd
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from ai_integration.situational_prompts import ORBDiscoveryPrompts

REAL_DATA_PATH = os.path.join(os.path.dirname(__file__), 'RealTestData')

def _require_real_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real market data required for all tests.')

def test_fail_on_missing_real_data(monkeypatch):
    monkeypatch.setattr(os.path, "exists", lambda path: False)
    with pytest.raises(FileNotFoundError, match="UNBREAKABLE RULE VIOLATION"):
        _require_real_data()

def test_get_core_situational_questions():
    _require_real_data()
    questions = ORBDiscoveryPrompts.get_orb_core_principles()
    assert isinstance(questions, list)
    assert all(isinstance(q, str) for q in questions)
    assert any("market" in q.lower() for q in questions)

def test_get_orb_examples():
    _require_real_data()
    examples = ORBDiscoveryPrompts.get_discovery_examples()
    assert isinstance(examples, list)
    assert all(isinstance(e, str) for e in examples)
    assert any("volatility" in e.lower() for e in examples)

def test_generate_discovery_prompt_success_and_regime_branches():
    _require_real_data()
    # STRONG UPTREND
    df = pd.DataFrame({'Open': [1, 2, 3, 4, 5], 'High': [2, 3, 4, 5, 6], 'Low': [0, 1, 2, 3, 4], 'Close': [1, 3, 5, 7, 10]})
    prompt = ORBDiscoveryPrompts.generate_stage1_discovery_prompt(df)
    assert isinstance(prompt, str)
    assert 'pattern discovery' in prompt.lower()
    regime = ORBDiscoveryPrompts._analyze_market_regime(df)
    assert 'STRONG UPTREND' in regime
    # LOW VOLATILITY (1% return - low volatility)
    df2 = pd.DataFrame({'Open': [100, 100.1, 100.2, 100.3, 100.4], 'High': [100.5, 100.6, 100.7, 100.8, 100.9], 'Low': [99.5, 99.6, 99.7, 99.8, 99.9], 'Close': [100, 100.2, 100.4, 100.6, 101]})
    regime = ORBDiscoveryPrompts._analyze_market_regime(df2)
    assert 'LOW VOLATILITY' in regime
    # STRONG DOWNTREND
    df3 = pd.DataFrame({'Open': [10, 9, 8, 7, 6], 'High': [10, 9, 8, 7, 6], 'Low': [9, 8, 7, 6, 5], 'Close': [10, 8, 6, 4, 2]})
    regime = ORBDiscoveryPrompts._analyze_market_regime(df3)
    assert 'STRONG DOWNTREND' in regime
    # LOW VOLATILITY (-1% return - low volatility)
    df4 = pd.DataFrame({'Open': [100, 99.8, 99.6, 99.4, 99.2], 'High': [100.2, 100, 99.8, 99.6, 99.4], 'Low': [99.8, 99.6, 99.4, 99.2, 99], 'Close': [100, 99.8, 99.6, 99.4, 99]})
    regime = ORBDiscoveryPrompts._analyze_market_regime(df4)
    assert 'LOW VOLATILITY' in regime
    # LOW VOLATILITY (small movements)
    df5 = pd.DataFrame({'Open': [10, 10.1, 9.9, 10, 10.05], 'High': [10.2, 10.3, 10.1, 10.2, 10.25], 'Low': [9.8, 9.9, 9.7, 9.8, 9.85], 'Close': [10, 10.05, 9.95, 10, 10.01]})
    regime = ORBDiscoveryPrompts._analyze_market_regime(df5)
    assert 'LOW VOLATILITY' in regime  # Updated expectation
    # Exception branch
    class Bad:
        def get(self, *a, **k): raise Exception('fail')
    regime = ORBDiscoveryPrompts._analyze_market_regime(Bad())
    assert 'Market regime analysis unavailable' in regime

def test_generate_discovery_prompt_missing_ohlc():
    _require_real_data()
    df = pd.DataFrame({'Open': [1], 'High': [2], 'Low': [0]})
    with pytest.raises(RuntimeError, match='UNBREAKABLE RULE VIOLATION: Missing OHLC columns:'):
        ORBDiscoveryPrompts.generate_stage1_discovery_prompt(df)
