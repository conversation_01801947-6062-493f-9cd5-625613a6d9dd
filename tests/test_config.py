import unittest
import importlib
import os
import importlib.util
import tempfile
import sys
from unittest.mock import patch, mock_open

class TestConfig(unittest.TestCase):
    def setUp(self):
        self.config_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src/config.py'))
        spec = importlib.util.spec_from_file_location('config', self.config_path)
        self.config_mod = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(self.config_mod)
        self.JaegerConfig = self.config_mod.JaegerConfig

    def test_env_loading_and_fields(self):
        """Test that all required fields are loaded from .env or defaults"""
        cfg = self.JaegerConfig()
        self.assertTrue(hasattr(cfg, 'LM_STUDIO_URL'))
        self.assertTrue(hasattr(cfg, 'LLM_TEMPERATURE'))
        self.assertTrue(hasattr(cfg, 'DEFAULT_STOP_LOSS_PCT'))
        self.assertTrue(hasattr(cfg, 'DEFAULT_TAKE_PROFIT_PCT'))
        self.assertTrue(cfg.LM_STUDIO_URL.startswith('http'))
        self.assertGreater(cfg.LLM_MAX_TOKENS, 100)
        self.assertGreater(cfg.DEFAULT_STOP_LOSS_PCT, 0)
        self.assertGreater(cfg.DEFAULT_TAKE_PROFIT_PCT, 0)

    def test_fail_fast_on_missing_env(self):
        """Test that missing required env variables cause failure"""
        orig = dict(os.environ)
        try:
            # Test missing DEFAULT_STOP_LOSS_PCT
            os.environ.pop('DEFAULT_STOP_LOSS_PCT', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_STOP_LOSS_PCT', str(cm.exception))
            
            # Restore and test missing DEFAULT_TAKE_PROFIT_PCT
            os.environ.update(orig)
            os.environ.pop('DEFAULT_TAKE_PROFIT_PCT', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_TAKE_PROFIT_PCT', str(cm.exception))
            
            # Test missing MT4_DEFAULT_LOT_SIZE
            os.environ.update(orig)
            os.environ.pop('MT4_DEFAULT_LOT_SIZE', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('MT4_DEFAULT_LOT_SIZE', str(cm.exception))
            
            # Test missing DEFAULT_INITIAL_CASH
            os.environ.update(orig)
            os.environ.pop('DEFAULT_INITIAL_CASH', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_INITIAL_CASH', str(cm.exception))
            
            # Test missing DEFAULT_MARGIN
            os.environ.update(orig)
            os.environ.pop('DEFAULT_MARGIN', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_MARGIN', str(cm.exception))
            
            # Test missing DEFAULT_SPREAD
            os.environ.update(orig)
            os.environ.pop('DEFAULT_SPREAD', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_SPREAD', str(cm.exception))
            
            # Test missing DEFAULT_COMMISSION
            os.environ.update(orig)
            os.environ.pop('DEFAULT_COMMISSION', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_COMMISSION', str(cm.exception))
            
            # Test missing DEFAULT_EXCLUSIVE_ORDERS
            os.environ.update(orig)
            os.environ.pop('DEFAULT_EXCLUSIVE_ORDERS', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_EXCLUSIVE_ORDERS', str(cm.exception))
            
            # Test missing DEFAULT_FINALIZE_TRADES
            os.environ.update(orig)
            os.environ.pop('DEFAULT_FINALIZE_TRADES', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_FINALIZE_TRADES', str(cm.exception))
            
            # Test missing DEFAULT_MAX_HOLDING_MINUTES
            os.environ.update(orig)
            os.environ.pop('DEFAULT_MAX_HOLDING_MINUTES', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_MAX_HOLDING_MINUTES', str(cm.exception))
            
            # Test missing DEFAULT_TRADE_ON_CLOSE
            os.environ.update(orig)
            os.environ.pop('DEFAULT_TRADE_ON_CLOSE', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_TRADE_ON_CLOSE', str(cm.exception))
            
            # Test missing DEFAULT_HEDGING
            os.environ.update(orig)
            os.environ.pop('DEFAULT_HEDGING', None)
            with self.assertRaises(ValueError) as cm:
                self.JaegerConfig()
            self.assertIn('DEFAULT_HEDGING', str(cm.exception))
            
        finally:
            os.environ.clear()
            os.environ.update(orig)

    def test_lm_studio_configuration(self):
        """Test LM Studio configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.LM_STUDIO_URL, str)
        self.assertIsInstance(cfg.LM_STUDIO_TIMEOUT, int)
        self.assertIsInstance(cfg.LLM_TEMPERATURE, float)
        self.assertIsInstance(cfg.LLM_MAX_TOKENS, int)
        self.assertGreaterEqual(cfg.LM_STUDIO_TIMEOUT, 0)
        self.assertGreaterEqual(cfg.LLM_TEMPERATURE, 0.0)
    
    def test_to_dict_functionality(self):
        """Test configuration to dictionary conversion"""
        cfg = self.JaegerConfig()
        config_dict = cfg.to_dict()
        
        self.assertIsInstance(config_dict, dict)
        self.assertIn('LM_STUDIO_URL', config_dict)
        self.assertIn('DEFAULT_STOP_LOSS_PCT', config_dict)
        self.assertIn('DEFAULT_TAKE_PROFIT_PCT', config_dict)
        
        # Ensure private attributes are not included
        for key in config_dict.keys():
            self.assertFalse(key.startswith('_'))
    
    def test_update_from_dict_functionality(self):
        """Test configuration update from dictionary"""
        cfg = self.JaegerConfig()
        original_temp = cfg.LLM_TEMPERATURE
        
        # Update with new values
        update_dict = {
            'LLM_TEMPERATURE': 0.8,
            'LLM_MAX_TOKENS': 2000,
            'LOG_LEVEL': 'DEBUG'
        }
        
        cfg.update_from_dict(update_dict)
        
        self.assertEqual(cfg.LLM_TEMPERATURE, 0.8)
        self.assertEqual(cfg.LLM_MAX_TOKENS, 2000)
        self.assertEqual(cfg.LOG_LEVEL, 'DEBUG')
        
        # Test with non-existent attribute (should be ignored)
        cfg.update_from_dict({'NON_EXISTENT_ATTR': 'value'})
        self.assertFalse(hasattr(cfg, 'NON_EXISTENT_ATTR'))
    
    def test_save_to_file_functionality(self):
        """Test configuration save to file"""
        cfg = self.JaegerConfig()
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.env') as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            cfg.save_to_file(tmp_path)
            
            # Verify file was created and contains expected content
            self.assertTrue(os.path.exists(tmp_path))
            
            with open(tmp_path, 'r') as f:
                content = f.read()
                self.assertIn('# Jaeger Configuration File', content)
                self.assertIn('LM_STUDIO_URL=', content)
                self.assertIn('DEFAULT_STOP_LOSS_PCT=', content)
                
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_load_from_file_functionality(self):
        """Test configuration load from file"""
        cfg = self.JaegerConfig()
        
        # Create a test config file
        test_config_content = """# Test Configuration
LLM_TEMPERATURE=0.9
LLM_MAX_TOKENS=1500
LLM_MAX_LEARNING_SESSIONS=5
LOG_LEVEL=WARNING
DEFAULT_EXCLUSIVE_ORDERS=false
DEFAULT_FINALIZE_TRADES=true
"""
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.env') as tmp_file:
            tmp_file.write(test_config_content)
            tmp_path = tmp_file.name
        
        try:
            cfg.load_from_file(tmp_path)
            
            # Verify values were loaded correctly
            self.assertEqual(cfg.LLM_TEMPERATURE, 0.9)
            self.assertEqual(cfg.LLM_MAX_TOKENS, 1500)
            self.assertEqual(cfg.LLM_MAX_LEARNING_SESSIONS, 5)
            self.assertEqual(cfg.LOG_LEVEL, 'WARNING')
            self.assertEqual(cfg.DEFAULT_EXCLUSIVE_ORDERS, False)
            self.assertEqual(cfg.DEFAULT_FINALIZE_TRADES, True)
            
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_load_from_nonexistent_file(self):
        """Test loading from non-existent file (should not raise error)"""
        cfg = self.JaegerConfig()
        original_temp = cfg.LLM_TEMPERATURE
        
        # Try to load from non-existent file
        cfg.load_from_file('/path/that/does/not/exist.env')
        
        # Configuration should remain unchanged
        self.assertEqual(cfg.LLM_TEMPERATURE, original_temp)
    
    def test_boolean_conversion_in_load_from_file(self):
        """Test boolean value conversion when loading from file"""
        cfg = self.JaegerConfig()
        
        # Test various boolean representations
        test_config_content = """DEFAULT_EXCLUSIVE_ORDERS=true
DEFAULT_FINALIZE_TRADES=false
DEFAULT_TRADE_ON_CLOSE=True
DEFAULT_HEDGING=FALSE
"""
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.env') as tmp_file:
            tmp_file.write(test_config_content)
            tmp_path = tmp_file.name
        
        try:
            cfg.load_from_file(tmp_path)
            
            self.assertEqual(cfg.DEFAULT_EXCLUSIVE_ORDERS, True)
            self.assertEqual(cfg.DEFAULT_FINALIZE_TRADES, False)
            self.assertEqual(cfg.DEFAULT_TRADE_ON_CLOSE, True)
            self.assertEqual(cfg.DEFAULT_HEDGING, False)
            
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_behavioral_intelligence_configuration(self):
        """Test behavioral intelligence configuration"""
        cfg = self.JaegerConfig()
        
        # Test default value
        self.assertIsInstance(cfg.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE, bool)
        
        # Test with environment variable set
        with patch.dict(os.environ, {'SIMPLIFIED_BEHAVIORAL_INTELLIGENCE': 'true'}):
            cfg_true = self.JaegerConfig()
            self.assertTrue(cfg_true.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE)
        
        with patch.dict(os.environ, {'SIMPLIFIED_BEHAVIORAL_INTELLIGENCE': 'false'}):
            cfg_false = self.JaegerConfig()
            self.assertFalse(cfg_false.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE)
        self.assertLessEqual(cfg.LLM_TEMPERATURE, 2.0)
        self.assertGreater(cfg.LLM_MAX_TOKENS, 0)

    def test_data_processing_configuration(self):
        """Test data processing configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.MIN_RECORDS_REQUIRED, int)
        self.assertIsInstance(cfg.MAX_MEMORY_USAGE_MB, int)
        self.assertIsInstance(cfg.DATA_VALIDATION_ENABLED, bool)
        self.assertGreater(cfg.MIN_RECORDS_REQUIRED, 0)
        self.assertGreater(cfg.MAX_MEMORY_USAGE_MB, 0)

    def test_trading_configuration(self):
        """Test trading configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.DEFAULT_STOP_LOSS_PCT, float)
        self.assertIsInstance(cfg.DEFAULT_TAKE_PROFIT_PCT, float)
        self.assertIsInstance(cfg.MAX_HOLDING_MINUTES, int)
        self.assertIsInstance(cfg.MIN_RISK_THRESHOLD, float)
        self.assertGreater(cfg.DEFAULT_STOP_LOSS_PCT, 0)
        self.assertGreater(cfg.DEFAULT_TAKE_PROFIT_PCT, 0)
        self.assertGreater(cfg.MAX_HOLDING_MINUTES, 0)
        self.assertGreaterEqual(cfg.MIN_RISK_THRESHOLD, 0)

    def test_backtesting_configuration(self):
        """Test backtesting configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.RISK_MULTIPLE, float)
        self.assertIsInstance(cfg.MAX_R_MULTIPLE, float)
        self.assertIsInstance(cfg.MIN_R_MULTIPLE, float)
        self.assertGreater(cfg.RISK_MULTIPLE, 0)
        self.assertGreater(cfg.MAX_R_MULTIPLE, cfg.MIN_R_MULTIPLE)

    def test_file_paths_configuration(self):
        """Test file paths are properly configured"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.DATA_DIR, str)
        self.assertIsInstance(cfg.RESULTS_DIR, str)
        self.assertIsInstance(cfg.LOG_FILE, str)
        self.assertTrue(os.path.isabs(cfg.DATA_DIR))
        self.assertTrue(os.path.isabs(cfg.RESULTS_DIR))
        self.assertTrue(os.path.isabs(cfg.LOG_FILE))

    def test_mt4_configuration(self):
        """Test MT4 configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.MT4_DEFAULT_LOT_SIZE, float)
        self.assertIsInstance(cfg.MT4_MAGIC_NUMBER, int)
        self.assertIsInstance(cfg.MT4_SLIPPAGE, int)
        self.assertGreater(cfg.MT4_DEFAULT_LOT_SIZE, 0)
        self.assertGreater(cfg.MT4_MAGIC_NUMBER, 0)
        self.assertGreaterEqual(cfg.MT4_SLIPPAGE, 0)

    def test_logging_configuration(self):
        """Test logging configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.LOG_LEVEL, str)
        self.assertIsInstance(cfg.LOG_TO_FILE, bool)
        self.assertIsInstance(cfg.LOG_TO_CONSOLE, bool)
        self.assertIn(cfg.LOG_LEVEL, ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'])

    def test_situational_analysis_configuration(self):
        """Test situational analysis configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.SITUATIONAL_ANALYSIS_ENABLED, bool)
        self.assertIsInstance(cfg.MIN_SITUATIONAL_SAMPLE_SIZE, int)
        self.assertIsInstance(cfg.SITUATIONAL_SIGNIFICANCE_THRESHOLD, float)
        self.assertIsInstance(cfg.BEHAVIORAL_CONSISTENCY_THRESHOLD, float)
        self.assertIsInstance(cfg.VOLATILITY_REGIME_PERIODS, int)
        self.assertIsInstance(cfg.SESSION_TRANSITION_HOURS, list)
        self.assertIsInstance(cfg.ENABLE_TOM_HOUGAARD_ANALYSIS, bool)
        self.assertGreater(cfg.MIN_SITUATIONAL_SAMPLE_SIZE, 0)
        self.assertGreaterEqual(cfg.SITUATIONAL_SIGNIFICANCE_THRESHOLD, 0)
        self.assertGreaterEqual(cfg.BEHAVIORAL_CONSISTENCY_THRESHOLD, 0)
        self.assertGreater(cfg.VOLATILITY_REGIME_PERIODS, 0)

    def test_validation_configuration(self):
        """Test validation configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.ENABLE_SITUATIONAL_VALIDATION, bool)
        self.assertIsInstance(cfg.MIN_CROSS_SITUATIONAL_SAMPLES, int)
        self.assertIsInstance(cfg.BEHAVIORAL_STABILITY_PERIODS, int)
        self.assertIsInstance(cfg.CONTEXT_ADJUSTED_MIN_TRADES, int)
        self.assertIsInstance(cfg.VOLATILITY_ADJUSTED_EXPECTATIONS, bool)
        self.assertIsInstance(cfg.SESSION_SPECIFIC_VALIDATION, bool)
        self.assertIsInstance(cfg.PATTERN_DEGRADATION_THRESHOLD, float)
        self.assertIsInstance(cfg.SITUATIONAL_CONSISTENCY_THRESHOLD, float)
        self.assertGreater(cfg.MIN_CROSS_SITUATIONAL_SAMPLES, 0)
        self.assertGreater(cfg.BEHAVIORAL_STABILITY_PERIODS, 0)
        self.assertGreater(cfg.CONTEXT_ADJUSTED_MIN_TRADES, 0)
        self.assertGreaterEqual(cfg.PATTERN_DEGRADATION_THRESHOLD, 0)
        self.assertGreaterEqual(cfg.SITUATIONAL_CONSISTENCY_THRESHOLD, 0)

    def test_walk_forward_configuration(self):
        """Test walk-forward testing configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.ENABLE_WALK_FORWARD_TESTING, bool)
        self.assertIsInstance(cfg.WALK_FORWARD_SPLITS, int)
        self.assertIsInstance(cfg.WALK_FORWARD_MIN_CONSISTENCY, float)
        self.assertGreater(cfg.WALK_FORWARD_SPLITS, 1)
        self.assertGreaterEqual(cfg.WALK_FORWARD_MIN_CONSISTENCY, 0)
        self.assertLessEqual(cfg.WALK_FORWARD_MIN_CONSISTENCY, 100)

    def test_v2_validation_configuration(self):
        """Test v2.0 validation configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.VALIDATION_INITIAL_CASH, float)
        self.assertIsInstance(cfg.VALIDATION_COMMISSION, float)
        self.assertIsInstance(cfg.VALIDATION_SPREAD, float)
        self.assertIsInstance(cfg.VALIDATION_MARGIN, float)
        self.assertIsInstance(cfg.VALIDATION_MIN_RETURN, float)
        self.assertIsInstance(cfg.VALIDATION_MIN_CONSISTENCY, float)
        self.assertIsInstance(cfg.VALIDATION_MIN_WIN_RATE, float)
        self.assertIsInstance(cfg.VALIDATION_MAX_DRAWDOWN, float)
        self.assertIsInstance(cfg.VALIDATION_MIN_DISTANCE, float)
        self.assertGreater(cfg.VALIDATION_INITIAL_CASH, 0)
        self.assertGreaterEqual(cfg.VALIDATION_COMMISSION, 0)
        self.assertGreaterEqual(cfg.VALIDATION_SPREAD, 0)
        self.assertGreater(cfg.VALIDATION_MARGIN, 0)
        self.assertGreaterEqual(cfg.VALIDATION_MIN_CONSISTENCY, 0)
        self.assertGreaterEqual(cfg.VALIDATION_MIN_WIN_RATE, 0)
        self.assertGreater(cfg.VALIDATION_MAX_DRAWDOWN, 0)
        self.assertGreater(cfg.VALIDATION_MIN_DISTANCE, 0)

    def test_dynamic_criteria_configuration(self):
        """Test dynamic criteria configuration"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.ENABLE_DYNAMIC_CRITERIA, bool)

    def test_backtesting_execution_configuration(self):
        """Test backtesting execution configuration parameters"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.DEFAULT_INITIAL_CASH, float)
        self.assertIsInstance(cfg.DEFAULT_MARGIN, float)
        self.assertIsInstance(cfg.DEFAULT_SPREAD, float)
        self.assertIsInstance(cfg.DEFAULT_COMMISSION, float)
        self.assertIsInstance(cfg.DEFAULT_EXCLUSIVE_ORDERS, bool)
        self.assertIsInstance(cfg.DEFAULT_FINALIZE_TRADES, bool)
        self.assertIsInstance(cfg.DEFAULT_MAX_HOLDING_MINUTES, int)
        self.assertIsInstance(cfg.DEFAULT_TRADE_ON_CLOSE, bool)
        self.assertIsInstance(cfg.DEFAULT_HEDGING, bool)
        self.assertGreater(cfg.DEFAULT_INITIAL_CASH, 0)
        self.assertGreater(cfg.DEFAULT_MARGIN, 0)
        self.assertGreaterEqual(cfg.DEFAULT_SPREAD, 0)
        self.assertGreaterEqual(cfg.DEFAULT_COMMISSION, 0)
        self.assertGreater(cfg.DEFAULT_MAX_HOLDING_MINUTES, 0)

    def test_behavioral_intelligence_configuration(self):
        """Test behavioral intelligence configuration"""
        cfg = self.JaegerConfig()
        self.assertIsInstance(cfg.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE, bool)

    def test_to_dict_method(self):
        """Test to_dict method returns all configuration as dictionary"""
        cfg = self.JaegerConfig()
        config_dict = cfg.to_dict()
        self.assertIsInstance(config_dict, dict)
        # Check that key attributes are in the dictionary
        self.assertIn('LM_STUDIO_URL', config_dict)
        self.assertIn('DEFAULT_STOP_LOSS_PCT', config_dict)
        self.assertIn('DEFAULT_TAKE_PROFIT_PCT', config_dict)
        self.assertIn('RISK_MULTIPLE', config_dict)
        self.assertIn('DATA_DIR', config_dict)
        # Verify values match the config object
        self.assertEqual(config_dict['LM_STUDIO_URL'], cfg.LM_STUDIO_URL)
        self.assertEqual(config_dict['DEFAULT_STOP_LOSS_PCT'], cfg.DEFAULT_STOP_LOSS_PCT)

    def test_update_from_dict_method(self):
        """Test update_from_dict method updates configuration from dictionary"""
        cfg = self.JaegerConfig()
        original_temp = cfg.LLM_TEMPERATURE
        original_tokens = cfg.LLM_MAX_TOKENS
        
        # Update with new values
        update_dict = {
            'LLM_TEMPERATURE': 0.9,
            'LLM_MAX_TOKENS': 2048
        }
        cfg.update_from_dict(update_dict)
        
        # Verify values were updated
        self.assertEqual(cfg.LLM_TEMPERATURE, 0.9)
        self.assertEqual(cfg.LLM_MAX_TOKENS, 2048)
        self.assertNotEqual(cfg.LLM_TEMPERATURE, original_temp)
        self.assertNotEqual(cfg.LLM_MAX_TOKENS, original_tokens)

    def test_save_to_file_method(self):
        """Test save_to_file method saves configuration to environment file"""
        cfg = self.JaegerConfig()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            # Save configuration to file
            cfg.save_to_file(tmp_path)
            
            # Verify file exists and contains environment format
            self.assertTrue(os.path.exists(tmp_path))
            
            with open(tmp_path, 'r') as f:
                content = f.read()
            
            # Verify file contains expected format
            self.assertIn('# Jaeger Configuration File', content)
            self.assertIn(f'LM_STUDIO_URL={cfg.LM_STUDIO_URL}', content)
            self.assertIn(f'DEFAULT_STOP_LOSS_PCT={cfg.DEFAULT_STOP_LOSS_PCT}', content)
            
        finally:
            # Clean up
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    def test_load_from_file_method(self):
        """Test load_from_file method loads configuration from environment file"""
        cfg = self.JaegerConfig()
        
        # Create test configuration data in environment format
        test_config_content = """# Test configuration
LLM_TEMPERATURE=0.8
LLM_MAX_TOKENS=1024
DEFAULT_STOP_LOSS_PCT=2.5
RISK_MULTIPLE=1.5
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as tmp_file:
            tmp_path = tmp_file.name
            tmp_file.write(test_config_content)
        
        try:
            # Load configuration from file
            cfg.load_from_file(tmp_path)
            
            # Verify values were loaded correctly
            self.assertEqual(cfg.LLM_TEMPERATURE, 0.8)
            self.assertEqual(cfg.LLM_MAX_TOKENS, 1024)
            self.assertEqual(cfg.DEFAULT_STOP_LOSS_PCT, 2.5)
            self.assertEqual(cfg.RISK_MULTIPLE, 1.5)
            
        finally:
            # Clean up
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    def test_load_from_file_with_type_conversion(self):
        """Test load_from_file handles type conversion correctly"""
        cfg = self.JaegerConfig()
        
        # Create test configuration with values that need type conversion
        test_config_content = """# Test configuration with type conversion
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=512
DATA_VALIDATION_ENABLED=True
LOG_TO_FILE=False
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as tmp_file:
            tmp_path = tmp_file.name
            tmp_file.write(test_config_content)
        
        try:
            # Load configuration from file
            cfg.load_from_file(tmp_path)
            
            # Verify type conversion worked correctly
            self.assertIsInstance(cfg.LLM_TEMPERATURE, float)
            self.assertEqual(cfg.LLM_TEMPERATURE, 0.7)
            self.assertIsInstance(cfg.LLM_MAX_TOKENS, int)
            self.assertEqual(cfg.LLM_MAX_TOKENS, 512)
            self.assertIsInstance(cfg.DATA_VALIDATION_ENABLED, bool)
            self.assertEqual(cfg.DATA_VALIDATION_ENABLED, True)
            self.assertIsInstance(cfg.LOG_TO_FILE, bool)
            self.assertEqual(cfg.LOG_TO_FILE, False)
            
        finally:
            # Clean up
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @patch('pathlib.Path.exists')
    def test_load_jaeger_env_file_not_found(self, mock_exists):
        """Test _load_jaeger_env raises exception when .env file not found"""
        mock_exists.return_value = False
        
        # This should raise a FileNotFoundError
        with self.assertRaises(FileNotFoundError) as context:
            self.config_mod._load_jaeger_env()
        self.assertIn('UNBREAKABLE RULE VIOLATION', str(context.exception))

    def test_load_from_file_nonexistent_file(self):
        """Test load_from_file handles nonexistent file gracefully"""
        cfg = self.JaegerConfig()
        original_temp = cfg.LLM_TEMPERATURE
        
        # Try to load from nonexistent file - should not change values
        cfg.load_from_file('/nonexistent/path/file.env')
        
        # Values should remain unchanged
        self.assertEqual(cfg.LLM_TEMPERATURE, original_temp)

    def test_load_from_file_invalid_lines(self):
        """Test load_from_file handles invalid lines gracefully"""
        cfg = self.JaegerConfig()
        original_temp = cfg.LLM_TEMPERATURE
        
        # Create test configuration with invalid lines
        test_config_content = """# Test configuration
INVALID_LINE_NO_EQUALS
LLM_TEMPERATURE=0.9
# Comment line

EMPTY_VALUE=
NONEXISTENT_ATTR=some_value
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as tmp_file:
            tmp_path = tmp_file.name
            tmp_file.write(test_config_content)
        
        try:
            # Load configuration from file
            cfg.load_from_file(tmp_path)
            
            # Valid line should be processed
            self.assertEqual(cfg.LLM_TEMPERATURE, 0.9)
            # Invalid lines should be ignored without error
            
        finally:
            # Clean up
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    def test_config_immutability_after_init(self):
        """Test that configuration values can be modified after initialization"""
        cfg = self.JaegerConfig()
        original_temp = cfg.LLM_TEMPERATURE
        
        # Modify a value
        cfg.LLM_TEMPERATURE = 1.5
        self.assertEqual(cfg.LLM_TEMPERATURE, 1.5)
        self.assertNotEqual(cfg.LLM_TEMPERATURE, original_temp)

    def test_config_attribute_access(self):
        """Test that all expected attributes are accessible"""
        cfg = self.JaegerConfig()
        
        # Test a sample of important attributes
        important_attrs = [
            'LM_STUDIO_URL', 'LLM_TEMPERATURE', 'LLM_MAX_TOKENS',
            'DEFAULT_STOP_LOSS_PCT', 'DEFAULT_TAKE_PROFIT_PCT',
            'RISK_MULTIPLE', 'DATA_DIR', 'RESULTS_DIR', 'LOG_FILE',
            'MT4_DEFAULT_LOT_SIZE', 'MT4_MAGIC_NUMBER'
        ]
        
        for attr in important_attrs:
            self.assertTrue(hasattr(cfg, attr), f"Missing attribute: {attr}")
            value = getattr(cfg, attr)
            self.assertIsNotNone(value, f"Attribute {attr} is None")

    def test_llm_max_learning_sessions_configuration(self):
        """Test LLM max learning sessions configuration"""
        cfg = self.JaegerConfig()

        # Test default value (updated to match actual config value)
        self.assertEqual(cfg.LLM_MAX_LEARNING_SESSIONS, 5)

        # Test environment variable override
        with patch.dict(os.environ, {'LLM_MAX_LEARNING_SESSIONS': '25'}):
            cfg_env = self.JaegerConfig()
            self.assertEqual(cfg_env.LLM_MAX_LEARNING_SESSIONS, 25)

        # Test invalid value handling
        with patch.dict(os.environ, {'LLM_MAX_LEARNING_SESSIONS': 'invalid'}):
            with self.assertRaises(ValueError):
                self.JaegerConfig()


if __name__ == '__main__':
    unittest.main()
