import unittest
import importlib
import sys
import os
from unittest.mock import patch, MagicMock

class TestBacktestingInit(unittest.TestCase):
    def setUp(self):
        """Set up test environment"""
        src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
        if src_dir not in sys.path:
            sys.path.insert(0, src_dir)
    
    def test_import(self):
        """Test basic module import"""
        try:
            module = importlib.import_module('backtesting')
            self.assertIsNotNone(module)
        except Exception as e:
            self.fail(f'Import failed: {e}')
    
    def test_version_attribute_exists(self):
        """Test that __version__ attribute exists"""
        import backtesting
        self.assertTrue(hasattr(backtesting, '__version__'))
        # Version should be either a real version or the fallback
        self.assertIsInstance(backtesting.__version__, str)
        self.assertTrue(len(backtesting.__version__) > 0)
    
    def test_pool_function_spawn_method(self):
        """Test Pool function with spawn start method"""
        import backtesting
        
        # Mock multiprocessing at the module level where it's imported
        with patch('multiprocessing.get_start_method', return_value='spawn') as mock_get_start, \
             patch('warnings.warn') as mock_warn, \
             patch('multiprocessing.dummy.Pool') as mock_dummy_pool_class:
            
            mock_dummy_pool_instance = MagicMock()
            mock_dummy_pool_class.return_value = mock_dummy_pool_instance
            
            result = backtesting.Pool(processes=2, initializer=None, initargs=())
            
            # Verify get_start_method was called
            mock_get_start.assert_called_once()
            
            # Verify warning was issued
            mock_warn.assert_called_once()
            warning_call = mock_warn.call_args
            self.assertIn('spawn', warning_call[0][0])
            self.assertEqual(warning_call[1]['category'], RuntimeWarning)
            self.assertEqual(warning_call[1]['stacklevel'], 3)
            
            # Verify dummy Pool was used
            mock_dummy_pool_class.assert_called_once_with(2, None, ())
            self.assertEqual(result, mock_dummy_pool_instance)
    
    def test_pool_function_non_spawn_method(self):
        """Test Pool function with non-spawn start method"""
        import backtesting
        
        # Mock multiprocessing at the module level where it's imported
        with patch('multiprocessing.get_start_method', return_value='fork') as mock_get_start, \
             patch('multiprocessing.Pool') as mock_mp_pool_class:
            
            mock_mp_pool_instance = MagicMock()
            mock_mp_pool_class.return_value = mock_mp_pool_instance
            
            result = backtesting.Pool(processes=4, initializer=None, initargs=(1, 2))
            
            # Verify get_start_method was called
            mock_get_start.assert_called_once()
            
            # Verify multiprocessing.Pool was used
            mock_mp_pool_class.assert_called_once_with(4, None, (1, 2))
            self.assertEqual(result, mock_mp_pool_instance)
    
    def test_module_imports(self):
        """Test that all expected imports are available"""
        import backtesting
        
        # Test that key imports are available
        self.assertTrue(hasattr(backtesting, 'lib'))
        self.assertTrue(hasattr(backtesting, 'set_bokeh_output'))
        self.assertTrue(hasattr(backtesting, 'Backtest'))
        self.assertTrue(hasattr(backtesting, 'Strategy'))
        self.assertTrue(hasattr(backtesting, 'Pool'))
        
        # Test that Pool is callable
        self.assertTrue(callable(backtesting.Pool))

if __name__ == '__main__':
    unittest.main()
