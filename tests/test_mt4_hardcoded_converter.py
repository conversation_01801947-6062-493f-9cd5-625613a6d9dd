#!/usr/bin/env python3

import unittest
import importlib.util
import os
import tempfile
import sys
from unittest.mock import Mock, MagicMock, patch

class TestMT4HardcodedConverter(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
        if src_dir not in sys.path:
            sys.path.insert(0, src_dir)
        target = os.path.join(src_dir, 'mt4_hardcoded_converter.py')
        if not os.path.exists(target):
            raise FileNotFoundError(f"UNBREAKABLE RULE VIOLATION: {target} not found.")
        spec = importlib.util.spec_from_file_location('mt4_hardcoded_converter', target)
        cls.mt4 = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(cls.mt4)
    
    def setUp(self):
        """Setup test fixtures"""
        from backtesting_rule_parser import TradingPattern

        # Create mock trading patterns for testing
        self.mock_rule_long = TradingPattern(
            pattern_name="Test Long Pattern",
            entry_conditions=[
                {"condition": "close_above_high", "lookback": 1}
            ],
            exit_conditions=[
                {"condition": "risk_reward_ratio", "risk": 1, "reward": 2}
            ],
            position_sizing={"method": "fixed_percent", "value": 0.001},
            optimal_conditions={"timeframes": ["M5"]}
        )

        self.mock_rule_short = TradingPattern(
            pattern_name="Test Short Pattern",
            entry_conditions=[
                {"condition": "close_below_low", "lookback": 1}
            ],
            exit_conditions=[
                {"condition": "risk_reward_ratio", "risk": 1, "reward": 1.5}
            ],
            position_sizing={"method": "fixed_percent", "value": 0.001},
            optimal_conditions={"timeframes": ["M15"]}
        )
    
    def test_no_hardcoded_params(self):
        """Test no hardcoded parameters"""
        file_path = os.path.join(os.path.dirname(__file__), '../src/mt4_hardcoded_converter.py')
        with open(file_path, 'r') as f:
            content = f.read()
        self.assertNotIn('="', content, msg='UNBREAKABLE RULE VIOLATION: Hardcoded parameter found in mt4_hardcoded_converter.py')
    
    def test_module_import(self):
        """Test module import"""
        self.assertIsNotNone(self.mt4.HardcodedMT4Converter)
        self.assertIsNotNone(self.mt4.convert_profitable_patterns_to_mt4)

    def test_failfast_on_unsupported_entry_logic(self):
        from backtesting_rule_parser import TradingPattern
        converter = self.mt4.HardcodedMT4Converter()
        rule = TradingPattern(
            pattern_name="Gipsy Danger 001",
            entry_conditions=[
                {"condition": "unsupported_condition", "value": "test"}
            ],
            exit_conditions=[
                {"condition": "fixed_stop_loss", "percentage": 0.02}
            ],
            position_sizing={"method": "fixed_percent", "value": 0.01},
            optimal_conditions={"timeframes": ["M15"]}
        )
        with self.assertRaises(RuntimeError) as ctx:
            converter.convert_rules_to_mt4([rule], ea_name='GipsyDanger_001')
        self.assertIn('UNBREAKABLE RULE VIOLATION', str(ctx.exception))

    def test_supported_entry_logic_conversion(self):
        from backtesting_rule_parser import TradingPattern
        converter = self.mt4.HardcodedMT4Converter()
        rule = TradingPattern(
            pattern_name="Gipsy Danger 002",
            entry_conditions=[
                {"condition": "close_above_high", "lookback": 1}
            ],
            exit_conditions=[
                {"condition": "risk_reward_ratio", "risk": 1, "reward": 2}
            ],
            position_sizing={"method": "fixed_percent", "value": 0.01},
            optimal_conditions={"timeframes": ["M15"]}
        )
        mq4_code = converter.convert_rules_to_mt4([rule], ea_name='GipsyDanger_002')
        self.assertIsInstance(mq4_code, str)
        self.assertIn('Gipsy Danger', mq4_code)
        self.assertIn('OnTick', mq4_code)
        self.assertIn('Close[0] > High[1]', mq4_code)
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'GipsyDanger_002.mq4')
            with open(output_path, 'w') as f:
                f.write(mq4_code)
            self.assertTrue(os.path.exists(output_path))
            with open(output_path) as f:
                content = f.read()
                self.assertIn('Gipsy Danger', content)

    def test_empty_ea_generation(self):
        """Test generation of empty EA when no profitable rules"""
        converter = self.mt4.HardcodedMT4Converter()
        empty_ea = converter.convert_rules_to_mt4([], ea_name='Empty_EA')
        self.assertIsInstance(empty_ea, str)
        self.assertIn('Empty_EA.mq4', empty_ea)
        self.assertIn('No Profitable Patterns Found', empty_ea)
        self.assertIn('Jaeger Trading System', empty_ea)

    def test_stop_logic_conversion_previous_low(self):
        """Test stop logic conversion for previous_low"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('previous_low', 'long')
        self.assertEqual(result, 'Low[1]')

    def test_stop_logic_conversion_previous_high(self):
        """Test stop logic conversion for previous_high"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('previous_high', 'short')
        self.assertEqual(result, 'High[1]')

    def test_stop_logic_conversion_current_low(self):
        """Test stop logic conversion for current_low"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('current_low', 'long')
        self.assertEqual(result, 'Low[0]')

    def test_stop_logic_conversion_current_high(self):
        """Test stop logic conversion for current_high"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('current_high', 'short')
        self.assertEqual(result, 'High[0]')

    def test_stop_logic_conversion_percentage_long(self):
        """Test stop logic conversion for percentage-based stops (long)"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('2.5% below entry', 'long')
        self.assertEqual(result, 'entryPrice * 0.975000')

    def test_stop_logic_conversion_percentage_short(self):
        """Test stop logic conversion for percentage-based stops (short)"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('3.0% above entry', 'short')
        self.assertEqual(result, 'entryPrice * 1.030000')

    def test_stop_logic_conversion_default_long(self):
        """Test stop logic conversion default for long"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('unknown logic', 'long')
        self.assertEqual(result, 'Low[1]')

    def test_stop_logic_conversion_default_short(self):
        """Test stop logic conversion default for short"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_stop_logic('unknown logic', 'short')
        self.assertEqual(result, 'High[1]')

    def test_target_logic_conversion_risk_reward_long(self):
        """Test target logic conversion for risk-reward based targets (long)"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic('entry_price + (entry_price - stop_price) * 2.0', 'long')
        self.assertEqual(result, 'entryPrice + (entryPrice - stopLoss) * 2.0')

    def test_target_logic_conversion_risk_reward_short(self):
        """Test target logic conversion for risk-reward based targets (short)"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic('entry_price - (stop_price - entry_price) * 1.5', 'short')
        self.assertEqual(result, 'entryPrice - (stopLoss - entryPrice) * 1.5')

    def test_target_logic_conversion_percentage_long(self):
        """Test target logic conversion for percentage-based targets (long)"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic('4.0% above entry', 'long')
        self.assertEqual(result, 'entryPrice * 1.040000')

    def test_target_logic_conversion_percentage_short(self):
        """Test target logic conversion for percentage-based targets (short)"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic('3.5% below entry', 'short')
        self.assertEqual(result, 'entryPrice * 0.965000')

    def test_target_logic_conversion_default_long(self):
        """Test target logic conversion default for long"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic('unknown logic', 'long')
        self.assertEqual(result, 'entryPrice + (entryPrice - stopLoss) * 2.0')

    def test_target_logic_conversion_default_short(self):
        """Test target logic conversion default for short"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic('unknown logic', 'short')
        self.assertEqual(result, 'entryPrice - (stopLoss - entryPrice) * 2.0')

    def test_get_entry_price_long(self):
        """Test entry price for long direction"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._get_entry_price('long')
        self.assertEqual(result, 'Ask')

    def test_get_entry_price_short(self):
        """Test entry price for short direction"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._get_entry_price('short')
        self.assertEqual(result, 'Bid')

    def test_generate_utility_functions(self):
        """Test utility functions generation"""
        converter = self.mt4.HardcodedMT4Converter()
        result = converter._generate_utility_functions()
        self.assertIsInstance(result, str)
        self.assertIn('IsTimeToTrade', result)
        self.assertIn('ValidateOrderParams', result)
        self.assertIn('CalculatePositionSize', result)
        self.assertIn('StartHour', result)
        self.assertIn('EndHour', result)
    
    def test_converter_initialization(self):
        """Test HardcodedMT4Converter initialization"""
        converter = self.mt4.HardcodedMT4Converter()
        self.assertEqual(converter.conversion_errors, [])
    
    def test_generate_ea_header(self):
        """Test EA header generation"""
        converter = self.mt4.HardcodedMT4Converter()
        header = converter._generate_ea_header("TestEA", 3)
        self.assertIn("TestEA.mq4", header)
        self.assertIn("#property copyright \"Jaeger Trading System\"", header)
        self.assertIn("3 validated patterns", header)
        self.assertIn("#property strict", header)
    
    def test_generate_input_parameters(self):
        """Test input parameters generation"""
        converter = self.mt4.HardcodedMT4Converter()
        rules = [self.mock_rule_long, self.mock_rule_short]
        params = converter._generate_input_parameters(rules)
        self.assertIn("input double LotSize", params)
        self.assertIn("input int MagicNumber", params)
        # Use actual generated rule IDs
        self.assertIn(f"input bool EnablePattern{self.mock_rule_long.rule_id}", params)
        self.assertIn(f"input bool EnablePattern{self.mock_rule_short.rule_id}", params)
        self.assertIn("Enable Test Long Pattern", params)
        self.assertIn("Enable Test Short Pattern", params)
    
    def test_generate_global_variables(self):
        """Test global variables generation"""
        converter = self.mt4.HardcodedMT4Converter()
        globals_code = converter._generate_global_variables()
        self.assertIn("int totalOrders", globals_code)
        self.assertIn("datetime lastBarTime", globals_code)
    
    def test_generate_init_function(self):
        """Test OnInit function generation"""
        converter = self.mt4.HardcodedMT4Converter()
        init_code = converter._generate_init_function()
        self.assertIn("int OnInit()", init_code)
        self.assertIn("void OnDeinit", init_code)
        self.assertIn("INIT_SUCCEEDED", init_code)
        self.assertIn("Jaeger Validated EA initialized", init_code)
    
    def test_generate_ontick_function(self):
        """Test OnTick function generation"""
        converter = self.mt4.HardcodedMT4Converter()
        rules = [self.mock_rule_long, self.mock_rule_short]
        ontick_code = converter._generate_ontick_function(rules)
        self.assertIn("void OnTick()", ontick_code)
        # Use actual generated rule IDs
        self.assertIn(f"if(EnablePattern{self.mock_rule_long.rule_id}) CheckPattern{self.mock_rule_long.rule_id}()", ontick_code)
        self.assertIn(f"if(EnablePattern{self.mock_rule_short.rule_id}) CheckPattern{self.mock_rule_short.rule_id}()", ontick_code)
        self.assertIn("Time[0] == lastBarTime", ontick_code)
        self.assertIn("IsTimeToTrade()", ontick_code)
    
    def test_generate_rule_function_long(self):
        """Test rule function generation for long position"""
        converter = self.mt4.HardcodedMT4Converter()
        rule_code = converter._generate_rule_function(self.mock_rule_long)
        self.assertIn(f"void CheckPattern{self.mock_rule_long.rule_id}()", rule_code)
        self.assertIn(f"Pattern {self.mock_rule_long.rule_id}: Test Long Pattern", rule_code)
        self.assertIn("OP_BUY", rule_code)
        self.assertIn("Close[0] > High[1]", rule_code)
        self.assertIn("Ask", rule_code)
        self.assertIn("Low[1]", rule_code)
    
    def test_generate_rule_function_short(self):
        """Test rule function generation for short position"""
        converter = self.mt4.HardcodedMT4Converter()
        rule_code = converter._generate_rule_function(self.mock_rule_short)
        self.assertIn(f"void CheckPattern{self.mock_rule_short.rule_id}()", rule_code)
        self.assertIn(f"Pattern {self.mock_rule_short.rule_id}: Test Short Pattern", rule_code)
        self.assertIn("OP_SELL", rule_code)  # Now correctly detects short direction
        self.assertIn("Close[0] < Low[1]", rule_code)
        self.assertIn("Bid", rule_code)
        self.assertIn("Low[1]", rule_code)  # Stop loss for short should be Low[1]
    
    def test_convert_entry_logic_basic_patterns(self):
        """Test entry logic conversion for basic patterns"""
        converter = self.mt4.HardcodedMT4Converter()
        test_cases = [
            ("current_close > previous_high", "Close[0] > High[1]"),
            ("current_close < previous_low", "Close[0] < Low[1]"),
            ("current_close > previous_close", "Close[0] > Close[1]"),
            ("current_close < previous_close", "Close[0] < Close[1]"),
            ("current_high > previous_high", "High[0] > High[1]"),
            ("current_low < previous_low", "Low[0] < Low[1]")
        ]
        
        for input_logic, expected_output in test_cases:
            result = converter._convert_entry_logic(input_logic)
            self.assertEqual(result, expected_output)
    
    def test_convert_entry_logic_with_backticks(self):
        """Test entry logic conversion with backticks"""
        converter = self.mt4.HardcodedMT4Converter()
        logic_with_backticks = "`current_close > previous_high`"
        result = converter._convert_entry_logic(logic_with_backticks)
        self.assertEqual(result, "Close[0] > High[1]")
    
    def test_convert_entry_logic_unknown_pattern(self):
        """Test entry logic conversion with unknown pattern"""
        converter = self.mt4.HardcodedMT4Converter()
        with self.assertRaises(RuntimeError) as context:
            converter._convert_entry_logic("unknown_pattern")
        self.assertIn("UNBREAKABLE RULE VIOLATION", str(context.exception))
    
    def test_convert_target_logic_entry_price_minus_pattern(self):
        """Test target logic conversion for entry_price - (stop_price - entry_price) pattern"""
        converter = self.mt4.HardcodedMT4Converter()
        # Test the specific pattern that covers line 264
        target_logic = "entry_price - (stop_price - entry_price) * 1.5"
        result = converter._convert_target_logic(target_logic, "short")
        self.assertEqual(result, "entryPrice - (stopLoss - entryPrice) * 1.5")
    
    def test_convert_profitable_patterns_to_mt4_function(self):
        """Test standalone conversion function"""
        rules = [self.mock_rule_long]
        with patch('builtins.print'):
            result = self.mt4.convert_profitable_patterns_to_mt4(rules, "Standalone_EA")
        self.assertIsInstance(result, str)
        self.assertIn("Standalone_EA.mq4", result)
        self.assertIn(f"CheckPattern{self.mock_rule_long.rule_id}()", result)
    
    def test_convert_profitable_patterns_to_mt4_empty(self):
        """Test standalone conversion function with empty rules"""
        result = self.mt4.convert_profitable_patterns_to_mt4([], "Empty_Standalone_EA")
        self.assertIsInstance(result, str)
        self.assertIn("Empty_Standalone_EA.mq4", result)
        self.assertIn("No Profitable Patterns Found", result)
    
    def test_multiple_rules_conversion(self):
        """Test conversion of multiple rules"""
        converter = self.mt4.HardcodedMT4Converter()
        rules = [self.mock_rule_long, self.mock_rule_short]
        with patch('builtins.print'):
            result = converter.convert_rules_to_mt4(rules, "Multi_EA")
        self.assertIn(f"EnablePattern{self.mock_rule_long.rule_id}", result)
        self.assertIn(f"EnablePattern{self.mock_rule_short.rule_id}", result)
        self.assertIn(f"CheckPattern{self.mock_rule_long.rule_id}()", result)
        self.assertIn(f"CheckPattern{self.mock_rule_short.rule_id}()", result)
    
    def test_fallback_entry_logic_coverage(self):
        """Test fallback entry logic to cover line 218"""
        from backtesting_rule_parser import BacktestingTradingRule
        
        # Create rule with unrecognized entry logic to trigger fallback
        rule_fallback = BacktestingTradingRule(
            rule_id=99,
            name="Fallback Test",
            market_logic="DAX",
            entry_logic="unrecognized_complex_logic_pattern",
            stop_logic="previous_low",
            target_logic="entry_price + 50",
            direction="long",
            position_size=0.1,
            timeframe="M5"
        )
        
        converter = self.mt4.HardcodedMT4Converter()
        
        # This should trigger the RuntimeError on line 218
        with self.assertRaises(RuntimeError) as context:
            converter._convert_entry_logic(rule_fallback.entry_logic)
        
        self.assertIn("UNBREAKABLE RULE VIOLATION", str(context.exception))
        self.assertIn("No fallback allowed", str(context.exception))
    
    def test_target_logic_subtraction_pattern_coverage(self):
        """Test target logic with subtraction pattern to cover line 269"""
        from backtesting_rule_parser import TradingPattern

        # Create rule with exact subtraction-based target logic pattern
        rule_subtraction = TradingPattern(
            pattern_name="Subtraction Target Test",
            entry_conditions=[
                {"condition": "close_above_high", "lookback": 1}
            ],
            exit_conditions=[
                {"condition": "risk_reward_ratio", "risk": 1, "reward": 2.5}
            ],
            position_sizing={"method": "fixed_percent", "value": 0.001},
            optimal_conditions={"timeframes": ["M5"]}
        )

        converter = self.mt4.HardcodedMT4Converter()
        result = converter._convert_target_logic(rule_subtraction.target_logic_text, rule_subtraction.direction)
        
        # Should handle the risk-reward pattern and extract multiplier 2.5
        self.assertIn("entryPrice + (entryPrice - stopLoss) * 2.5", result)
    


if __name__ == '__main__':
    unittest.main()
