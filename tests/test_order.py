import os
import pytest
import pandas as pd
from backtesting.backtesting import Order
from backtesting.backtesting import _Broker

REAL_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv'))

@pytest.fixture(scope='module')
def real_market_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data not found at {}".format(REAL_DATA_PATH))
    df = pd.read_csv(REAL_DATA_PATH)
    # Enforce strict OHLCV capitalization
    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_cols:
        if col not in df.columns:
            raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
    return df

def make_broker(df):
    return _Broker(
        data=df,
        cash=100000,
        spread=1.0,
        commission=(0, 0),
        margin=0.01,
        trade_on_close=False,
        hedging=False,
        exclusive_orders=False,
        index=df.index
    )

def test_order_creation_and_repr(real_market_data):
    broker = make_broker(real_market_data)
    order = Order(broker, size=1, limit_price=real_market_data['Close'][0]+10, stop_price=None, sl_price=None, tp_price=None, parent_trade=None, tag='test')
    assert order.size == 1
    assert order.is_long
    assert not order.is_short
    assert 'Order' in repr(order)

def test_order_cancel(real_market_data):
    broker = make_broker(real_market_data)
    order = Order(broker, size=1, limit_price=real_market_data['Close'][0]+10, stop_price=None, sl_price=None, tp_price=None, parent_trade=None, tag='cancel')
    broker.orders.append(order)
    order.cancel()
    assert order not in broker.orders
