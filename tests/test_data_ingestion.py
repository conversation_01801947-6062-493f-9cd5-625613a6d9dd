"""Tests for DataIngestion module"""

import pytest
import pandas as pd
import unittest
import os
import sys
import importlib.util
from unittest.mock import Mock, MagicMock, patch, mock_open
import numpy as np
import tempfile

# Add src directory to path
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from data_ingestion import DataIngestionManager


class TestDataIngestion(unittest.TestCase):
    """Test suite for DataIngestion class"""
    
    def setUp(self):
        """Setup test fixtures"""
        self.path = os.path.join(os.path.dirname(__file__), '../src/data_ingestion.py')
        spec = importlib.util.spec_from_file_location('data_ingestion', self.path)
        self.module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(self.module)
        
        self.manager = DataIngestionManager()
        
        self.real_data_path = os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_200_bars.csv')
        
        # Create sample OHLC data for testing
        self.sample_ohlc_data = pd.DataFrame({
            'Open': [100.0, 101.0, 102.0, 103.0, 104.0],
            'High': [101.5, 102.5, 103.5, 104.5, 105.5],
            'Low': [99.5, 100.5, 101.5, 102.5, 103.5],
            'Close': [101.0, 102.0, 103.0, 104.0, 105.0],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
    def test_import(self):
        """Test module import"""
        self.assertIsNotNone(self.module)

    def test_no_hardcoded_params(self):
        """Test no hardcoded parameters"""
        with open(self.path, 'r') as f:
            content = f.read()
        self.assertNotIn('="', content, msg='UNBREAKABLE RULE VIOLATION: Hardcoded parameter found in data_ingestion.py')
        
    def test_real_data_only(self):
        """Test real data requirement"""
        if not os.path.exists(self.real_data_path):
            self.skipTest('UNBREAKABLE RULE VIOLATION: Real data required in /tests/RealTestData/dax_200_bars.csv')
        df = pd.read_csv(self.real_data_path)
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_cols:
            self.assertIn(col, df.columns, f'Missing column: {col}')

    def test_strict_ohlc_capitalization(self):
        """Test strict OHLC capitalization"""
        if not os.path.exists(self.real_data_path):
            self.skipTest('Real data file not found')
            
        df = pd.read_csv(self.real_data_path)
        for col in ['Open', 'High', 'Low', 'Close']:
            self.assertIn(col, df.columns)
        for col in df.columns:
            if col.lower() in ['open', 'high', 'low', 'close']:
                self.assertIn(col, ['Open', 'High', 'Low', 'Close'])
                
    def test_data_ingestion_manager_init(self):
        """Test DataIngestionManager class initialization"""
        manager = DataIngestionManager()
        self.assertIsNotNone(manager)
        self.assertEqual(manager.supported_formats, ['.csv', '.xlsx', '.xls'])
        self.assertEqual(manager.required_columns, ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume'])
            
    def test_load_market_data_csv(self):
        """Test CSV market data loading functionality"""
        # Create temporary CSV file with DateTime column and sufficient data (50+ rows)
        test_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=60, freq='1h'),
            'Open': [100.0 + i for i in range(60)],
            'High': [101.0 + i for i in range(60)],
            'Low': [99.0 + i for i in range(60)],
            'Close': [100.5 + i for i in range(60)],
            'Volume': [1000 + i*10 for i in range(60)]
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
            
        try:
            df = self.manager.load_market_data(temp_file)
            self.assertIsInstance(df, pd.DataFrame)
            self.assertEqual(len(df), 60)
            self.assertIsInstance(df.index, pd.DatetimeIndex)
            
        finally:
            os.unlink(temp_file)
            
    def test_load_market_data_insufficient_data(self):
        """Test loading data with insufficient rows"""
        # Create data with less than 50 rows
        small_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=10, freq='1h'),
            'Open': [100.0] * 10,
            'High': [101.0] * 10,
            'Low': [99.0] * 10,
            'Close': [100.5] * 10,
            'Volume': [1000] * 10
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            small_data.to_csv(f.name, index=False)
            temp_file = f.name
            
        try:
            result = self.manager.load_market_data(temp_file)
            self.assertIsNone(result)
        finally:
            os.unlink(temp_file)
            
    def test_load_market_data_file_not_found(self):
        """Test loading non-existent file"""
        with self.assertRaises(FileNotFoundError):
            self.manager.load_market_data('non_existent_file.csv')
            
    def test_load_market_data_unsupported_format(self):
        """Test loading unsupported file format"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file = f.name
            
        try:
            with self.assertRaises(ValueError):
                self.manager.load_market_data(temp_file)
        finally:
            os.unlink(temp_file)
                
    def test_validate_and_prepare_data(self):
        """Test data validation and preparation"""
        # Test valid data with DateTime
        test_data = self.sample_ohlc_data.copy()
        test_data['DateTime'] = pd.date_range('2023-01-01', periods=5, freq='1h')
        
        result = self.manager._validate_and_prepare_data(test_data)
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIsInstance(result.index, pd.DatetimeIndex)
        
    def test_validate_and_prepare_data_missing_datetime(self):
        """Test data preparation with missing DateTime column"""
        # Test data without DateTime but with Date and Time
        test_data = self.sample_ohlc_data.copy()
        test_data['Date'] = '2023-01-01'
        test_data['Time'] = ['10:00:00', '11:00:00', '12:00:00', '13:00:00', '14:00:00']
        
        result = self.manager._validate_and_prepare_data(test_data)
        self.assertIsInstance(result, pd.DataFrame)
        # After processing, DateTime becomes the index, so it won't be in columns
        self.assertIsInstance(result.index, pd.DatetimeIndex)
        
    def test_validate_and_prepare_data_missing_columns(self):
        """Test data preparation with missing required columns"""
        invalid_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=2, freq='1h'),
            'Open': [100, 101],
            'High': [101, 102]
            # Missing Low, Close, Volume
        })
        
        with self.assertRaises(ValueError):
            self.manager._validate_and_prepare_data(invalid_data)
            
    def test_validate_ohlc_integrity(self):
        """Test OHLC data integrity validation"""
        # Test valid data
        test_data = pd.DataFrame({
            'Open': [100.0, 101.0],
            'High': [102.0, 103.0],
            'Low': [99.0, 100.0],
            'Close': [101.0, 102.0],
            'Volume': [1000, 1100]
        })
        
        # Should not raise exception
        self.manager._validate_ohlc_integrity(test_data)
        
    def test_validate_ohlc_integrity_negative_prices(self):
        """Test OHLC integrity with negative prices"""
        invalid_data = pd.DataFrame({
            'Open': [-100.0, 101.0],
            'High': [102.0, 103.0],
            'Low': [99.0, 100.0],
            'Close': [101.0, 102.0],
            'Volume': [1000, 1100]
        })
        
        with self.assertRaises(ValueError):
            self.manager._validate_ohlc_integrity(invalid_data)
            
    def test_validate_ohlc_integrity_invalid_relationships(self):
        """Test OHLC integrity with invalid High/Low relationships"""
        invalid_data = pd.DataFrame({
            'Open': [100.0, 101.0],
            'High': [98.0, 103.0],  # High < Open
            'Low': [102.0, 100.0],  # Low > Close
            'Close': [101.0, 102.0],
            'Volume': [1000, 1100]
        })
        
        # Should fix the invalid relationships, not raise exception
        self.manager._validate_ohlc_integrity(invalid_data)
        
        # Check that High was corrected
        self.assertGreaterEqual(invalid_data.loc[0, 'High'], invalid_data.loc[0, 'Open'])
        self.assertLessEqual(invalid_data.loc[0, 'Low'], invalid_data.loc[0, 'Close'])
            
    def test_prepare_for_backtesting(self):
        """Test data preparation for backtesting.py"""
        # Create test data with datetime index
        test_data = self.sample_ohlc_data.copy()
        test_data.index = pd.date_range('2023-01-01', periods=5, freq='1h')
        
        result = self.manager.prepare_for_backtesting(test_data)
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIsInstance(result.index, pd.DatetimeIndex)
        
        # Check column order
        expected_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        self.assertEqual(list(result.columns), expected_columns)
        
        # Check data types
        for col in ['Open', 'High', 'Low', 'Close']:
            self.assertEqual(result[col].dtype, float)
        self.assertEqual(result['Volume'].dtype, int)
        
    def test_get_data_summary(self):
        """Test data summary generation"""
        # Create test data with datetime index
        test_data = self.sample_ohlc_data.copy()
        test_data.index = pd.date_range('2023-01-01', periods=5, freq='1h')
        
        summary = self.manager.get_data_summary(test_data)
        
        self.assertIsInstance(summary, dict)
        self.assertIn('total_rows', summary)
        self.assertIn('date_range', summary)
        self.assertIn('price_range', summary)
        self.assertIn('volume_stats', summary)
        
        self.assertEqual(summary['total_rows'], 5)
        self.assertIn('start', summary['date_range'])
        self.assertIn('end', summary['date_range'])
        
    def test_load_data_file_excel(self):
        """Test Excel file loading"""
        # Create temporary Excel file
        test_data = self.sample_ohlc_data.copy()
        test_data['DateTime'] = pd.date_range('2023-01-01', periods=5, freq='1h')
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            test_data.to_excel(f.name, index=False)
            temp_file = f.name
            
        try:
            result = self.manager._load_data_file(temp_file)
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 5)
        finally:
            os.unlink(temp_file)
            
    def test_bid_ask_format_conversion(self):
        """Test conversion from Bid format to OHLC"""
        # Create data with Bid column instead of OHLC
        bid_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=5, freq='1h'),
            'Bid': [100.0, 101.0, 102.0, 103.0, 104.0],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        result = self.manager._validate_and_prepare_data(bid_data)
        
        # Should have synthesized OHLC from Bid
        self.assertIn('Open', result.columns)
        self.assertIn('High', result.columns)
        self.assertIn('Low', result.columns)
        self.assertIn('Close', result.columns)
        
        # All OHLC values should equal Bid values
        self.assertTrue((result['Open'] == bid_data['Bid']).all())
        self.assertTrue((result['High'] == bid_data['Bid']).all())
        self.assertTrue((result['Low'] == bid_data['Bid']).all())
        self.assertTrue((result['Close'] == bid_data['Bid']).all())
            
    def test_data_preprocessing(self):
        """Test data preprocessing functionality"""
        if hasattr(self.module, 'preprocess_data') or (hasattr(self.module, 'DataIngestion') and hasattr(self.module.DataIngestion(), 'preprocess')):
            if hasattr(self.module, 'preprocess_data'):
                processed = self.module.preprocess_data(self.sample_ohlc_data)
            else:
                ingestion = self.module.DataIngestion()
                processed = ingestion.preprocess(self.sample_ohlc_data)
                
            self.assertIsInstance(processed, pd.DataFrame)
            
    def test_data_format_conversion(self):
        """Test data format conversion"""
        if hasattr(self.module, 'convert_format'):
            converted = self.module.convert_format(self.sample_ohlc_data, 'standard')
            self.assertIsInstance(converted, pd.DataFrame)
            
    def test_data_validation_edge_cases(self):
        """Test data validation edge cases"""
        if hasattr(self.module, 'validate_ohlc_data'):
            # Empty DataFrame
            empty_df = pd.DataFrame()
            result = self.module.validate_ohlc_data(empty_df)
            self.assertFalse(result)
            
            # DataFrame with wrong data types
            wrong_types = pd.DataFrame({
                'Open': ['a', 'b', 'c'],
                'High': ['d', 'e', 'f'],
                'Low': ['g', 'h', 'i'],
                'Close': ['j', 'k', 'l'],
                'Volume': ['m', 'n', 'o']
            })
            
            result = self.module.validate_ohlc_data(wrong_types)
            self.assertFalse(result)
            
    def test_file_handling_errors(self):
        """Test file handling error cases"""
        if hasattr(self.module, 'load_csv_data'):
            # Test non-existent file
            try:
                result = self.module.load_csv_data('non_existent_file.csv')
                # Should either return None or raise exception
                if result is not None:
                    self.assertIsInstance(result, pd.DataFrame)
            except (FileNotFoundError, Exception):
                # Expected behavior
                pass
                
    def test_data_integrity_checks(self):
        """Test data integrity validation"""
        if hasattr(self.module, 'check_data_integrity'):
            # Test with valid data
            result = self.module.check_data_integrity(self.sample_ohlc_data)
            self.assertTrue(result)
            
            # Test with invalid OHLC relationships
            invalid_ohlc = self.sample_ohlc_data.copy()
            invalid_ohlc.loc[0, 'High'] = 90  # High < Open, Low, Close
            
            result = self.module.check_data_integrity(invalid_ohlc)
            self.assertFalse(result)
            
    def test_data_source_handling(self):
        """Test different data source handling"""
        if hasattr(self.module, 'DataIngestion'):
            ingestion = self.module.DataIngestion()
            
            # Test different source types if supported
            if hasattr(ingestion, 'load_from_source'):
                # Mock different sources
                with patch.object(ingestion, 'load_from_source') as mock_load:
                    mock_load.return_value = self.sample_ohlc_data
                    
                    result = ingestion.load_from_source('csv', 'test.csv')
                    self.assertIsInstance(result, pd.DataFrame)
                    
    def test_data_caching(self):
        """Test data caching functionality if available"""
        if hasattr(self.module, 'DataIngestion'):
            ingestion = self.module.DataIngestion()
            
            if hasattr(ingestion, 'cache_data'):
                # Test caching
                ingestion.cache_data('test_key', self.sample_ohlc_data)
                
                if hasattr(ingestion, 'get_cached_data'):
                    cached = ingestion.get_cached_data('test_key')
                    pd.testing.assert_frame_equal(cached, self.sample_ohlc_data)
                    
    def test_data_filtering(self):
        """Test data filtering functionality"""
        if hasattr(self.module, 'filter_data'):
            # Test date range filtering
            filtered = self.module.filter_data(self.sample_ohlc_data, start_date='2023-01-01')
            self.assertIsInstance(filtered, pd.DataFrame)
            
    def test_data_aggregation(self):
        """Test data aggregation functionality"""
        if hasattr(self.module, 'aggregate_data'):
            # Test timeframe aggregation
            aggregated = self.module.aggregate_data(self.sample_ohlc_data, '1H')
            self.assertIsInstance(aggregated, pd.DataFrame)
            
    def test_data_export(self):
        """Test data export functionality"""
        if hasattr(self.module, 'export_data') or (hasattr(self.module, 'DataIngestion') and hasattr(self.module.DataIngestion(), 'export')):
            with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
                temp_file = f.name
                
            try:
                if hasattr(self.module, 'export_data'):
                    self.module.export_data(self.sample_ohlc_data, temp_file)
                else:
                    ingestion = self.module.DataIngestion()
                    ingestion.export(self.sample_ohlc_data, temp_file)
                    
                # Verify file was created
                self.assertTrue(os.path.exists(temp_file))
                
                # Verify data integrity
                exported_df = pd.read_csv(temp_file)
                self.assertEqual(len(exported_df), len(self.sample_ohlc_data))
                
            finally:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    
    def test_real_data_structure(self):
        """Test real data structure and quality"""
        if os.path.exists(self.real_data_path):
            df = pd.read_csv(self.real_data_path)
            
            # Test data quality
            self.assertGreater(len(df), 0, "Real data should not be empty")
            self.assertGreaterEqual(len(df), 200, "Should have at least 200 bars for testing")
            
            # Test OHLC relationships
            for idx, row in df.iterrows():
                self.assertGreaterEqual(row['High'], row['Open'], f"High should be >= Open at row {idx}")
                self.assertGreaterEqual(row['High'], row['Close'], f"High should be >= Close at row {idx}")
                self.assertLessEqual(row['Low'], row['Open'], f"Low should be <= Open at row {idx}")
                self.assertLessEqual(row['Low'], row['Close'], f"Low should be <= Close at row {idx}")
                self.assertGreater(row['Volume'], 0, f"Volume should be positive at row {idx}")
                
    def test_data_types_validation(self):
        """Test data types validation"""
        if os.path.exists(self.real_data_path):
            df = pd.read_csv(self.real_data_path)
            
            # Check numeric columns
            numeric_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            for col in numeric_cols:
                if col in df.columns:
                    self.assertTrue(pd.api.types.is_numeric_dtype(df[col]) or 
                                  df[col].dtype == 'object',  # Might need conversion
                                  f"{col} should be numeric or convertible to numeric")
                    
    def test_missing_data_handling_in_validation(self):
        """Test missing data handling in OHLC validation"""
        # Create data with missing values
        data_with_na = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=5, freq='1h'),
            'Open': [100.0, 101.0, np.nan, 103.0, 104.0],
            'High': [101.5, 102.5, 103.5, np.nan, 105.5],
            'Low': [99.5, 100.5, 101.5, 102.5, 103.5],
            'Close': [101.0, 102.0, 103.0, 104.0, 105.0],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        # Should handle missing values by forward filling
        result = self.manager._validate_and_prepare_data(data_with_na)
        self.assertFalse(result.isnull().any().any())
        
    def test_duplicate_timestamps_removal(self):
        """Test removal of duplicate timestamps"""
        # Create data with duplicate timestamps
        duplicate_data = pd.DataFrame({
            'DateTime': ['2023-01-01 10:00:00', '2023-01-01 10:00:00', '2023-01-01 11:00:00'],
            'Open': [100.0, 100.5, 101.0],
            'High': [101.0, 101.5, 102.0],
            'Low': [99.0, 99.5, 100.0],
            'Close': [100.5, 101.0, 101.5],
            'Volume': [1000, 1100, 1200]
        })
        
        result = self.manager._validate_and_prepare_data(duplicate_data)
        # Should remove duplicates, keeping first occurrence
        self.assertEqual(len(result), 2)
        
    def test_datetime_column_synthesis_from_lowercase(self):
        """Test DateTime synthesis from lowercase datetime column"""
        test_data = self.sample_ohlc_data.copy()
        test_data['datetime'] = pd.date_range('2023-01-01', periods=5, freq='1h')
        
        result = self.manager._validate_and_prepare_data(test_data)
        self.assertIsInstance(result.index, pd.DatetimeIndex)
        
    def test_excel_file_loading(self):
        """Test Excel file loading path"""
        # Create temporary Excel file with sufficient data
        test_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=60, freq='1h'),
            'Open': [100.0 + i for i in range(60)],
            'High': [101.0 + i for i in range(60)],
            'Low': [99.0 + i for i in range(60)],
            'Close': [100.5 + i for i in range(60)],
            'Volume': [1000 + i*10 for i in range(60)]
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False) as f:
            test_data.to_excel(f.name, index=False)
            temp_file = f.name
            
        try:
            df = self.manager.load_market_data(temp_file)
            self.assertIsInstance(df, pd.DataFrame)
            self.assertEqual(len(df), 60)
        finally:
            os.unlink(temp_file)
            
    def test_data_summary_comprehensive(self):
        """Test comprehensive data summary generation"""
        # Create test data with datetime index
        test_data = pd.DataFrame({
            'Open': [100.0, 101.0, 102.0, 103.0, 104.0],
            'High': [101.5, 102.5, 103.5, 104.5, 105.5],
            'Low': [99.5, 100.5, 101.5, 102.5, 103.5],
            'Close': [101.0, 102.0, 103.0, 104.0, 105.0],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        test_data.index = pd.date_range('2023-01-01', periods=5, freq='1h')
        
        summary = self.manager.get_data_summary(test_data)
        
        self.assertIsInstance(summary, dict)
        self.assertEqual(summary['total_rows'], 5)
        self.assertIn('date_range', summary)
        self.assertIn('price_range', summary)
        self.assertIn('volume_stats', summary)
        
        # Check price range details
        self.assertIn('min_low', summary['price_range'])
        self.assertIn('max_high', summary['price_range'])
        self.assertIn('avg_close', summary['price_range'])
        
        # Check volume stats
        self.assertIn('total_volume', summary['volume_stats'])
        self.assertIn('avg_volume', summary['volume_stats'])
        self.assertIn('max_volume', summary['volume_stats'])
        
    def test_prepare_for_backtesting_non_datetime_index(self):
        """Test prepare_for_backtesting with non-datetime index"""
        test_data = self.sample_ohlc_data.copy()
        # Set a string index that needs conversion
        test_data.index = ['2023-01-01 10:00:00', '2023-01-01 11:00:00', 
                          '2023-01-01 12:00:00', '2023-01-01 13:00:00', 
                          '2023-01-01 14:00:00']
        
        result = self.manager.prepare_for_backtesting(test_data)
        self.assertIsInstance(result.index, pd.DatetimeIndex)
        
    def test_load_data_file_none_return(self):
        """Test _load_data_file returning None scenario"""
        # Create a CSV file that will return None from load_market_data
        small_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=5, freq='1h'),
            'Open': [100.0] * 5,
            'High': [101.0] * 5,
            'Low': [99.0] * 5,
            'Close': [100.5] * 5,
            'Volume': [1000] * 5
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            small_data.to_csv(f.name, index=False)
            temp_file = f.name
            
        try:
            # This should return None due to insufficient data (< 50 rows)
            result = self.manager.load_market_data(temp_file)
            self.assertIsNone(result)
        finally:
            os.unlink(temp_file)
            
    def test_validate_and_prepare_data_missing_datetime_no_fallback(self):
        """Test data preparation when DateTime cannot be synthesized"""
        test_data = self.sample_ohlc_data.copy()
        # No DateTime, Date, Time, or datetime columns
        
        with self.assertRaises(ValueError) as context:
            self.manager._validate_and_prepare_data(test_data)
        self.assertIn("Missing required column: DateTime", str(context.exception))
        
    def test_load_data_file_xlsx_format(self):
        """Test loading .xlsx format specifically"""
        test_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=60, freq='1h'),
            'Open': [100.0 + i for i in range(60)],
            'High': [101.0 + i for i in range(60)],
            'Low': [99.0 + i for i in range(60)],
            'Close': [100.5 + i for i in range(60)],
            'Volume': [1000 + i*10 for i in range(60)]
        })
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            test_data.to_excel(f.name, index=False)
            temp_file = f.name
            
        try:
            # Test the Excel loading path specifically (non-CSV path)
            result = self.manager._load_data_file(temp_file)
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 60)
        finally:
            os.unlink(temp_file)
            
    def test_load_data_file_csv_path_specifically(self):
        """Test CSV loading path specifically"""
        test_data = pd.DataFrame({
            'DateTime': pd.date_range('2023-01-01', periods=60, freq='1h'),
            'Open': [100.0 + i for i in range(60)],
            'High': [101.0 + i for i in range(60)],
            'Low': [99.0 + i for i in range(60)],
            'Close': [100.5 + i for i in range(60)],
            'Volume': [1000 + i*10 for i in range(60)]
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
            
        try:
            # Test the CSV loading path specifically
            result = self.manager._load_data_file(temp_file)
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 60)
        finally:
            os.unlink(temp_file)
            
    def test_validate_ohlc_integrity_with_missing_values_and_fixes(self):
        """Test OHLC integrity validation with missing values and automatic fixes"""
        # Create data with missing values and invalid relationships
        test_data = pd.DataFrame({
            'Open': [100.0, 101.0, np.nan, 103.0, 104.0],
            'High': [98.0, 102.5, 103.5, 102.0, 105.5],  # First High < Open, fourth High < Close
            'Low': [99.5, 100.5, 101.5, 105.0, 103.5],   # Fourth Low > Open and Close
            'Close': [101.0, 102.0, 103.0, 104.0, np.nan],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        # This should handle missing values and fix invalid relationships
        self.manager._validate_ohlc_integrity(test_data)
        
        # Check that missing values were forward filled
        self.assertFalse(test_data.isnull().any().any())
        
        # Check that invalid relationships were fixed
        self.assertGreaterEqual(test_data.loc[0, 'High'], test_data.loc[0, 'Open'])
        self.assertLessEqual(test_data.loc[3, 'Low'], test_data.loc[3, 'Open'])
        self.assertLessEqual(test_data.loc[3, 'Low'], test_data.loc[3, 'Close'])
            

if __name__ == '__main__':
    unittest.main()
