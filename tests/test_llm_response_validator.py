#!/usr/bin/env python3
"""
Comprehensive test suite for LLM Response Validation and Correction System
UNBREAKABLE RULE: Uses ONLY real market data from /tests/RealTestData
"""

import unittest
import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from llm_response_validator import LLMResponseValidator
from backtesting_rule_parser import BacktestingRuleParseError

class MockLLMClient:
    """Mock LLM client for testing correction behavior"""
    
    def __init__(self, correction_responses=None):
        self.correction_responses = correction_responses or []
        self.call_count = 0
        self.sent_messages = []
    
    def send_message(self, message, **kwargs):
        """Mock send_message that returns predefined responses"""
        self.sent_messages.append(message)
        
        if self.call_count < len(self.correction_responses):
            response = self.correction_responses[self.call_count]
            self.call_count += 1
            return response
        
        # Default fallback response
        return json.dumps({
            "pattern_name": "Corrected Pattern",
            "entry_conditions": [{"condition": "close_above_high", "lookback": 1}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })

class TestLLMResponseValidator(unittest.TestCase):
    """Test suite for LLM Response Validation and Correction System"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.validator = LLMResponseValidator(max_retries=2)
        self.mock_client = MockLLMClient()
    
    def test_valid_response_no_correction(self):
        """Test that valid responses don't trigger unnecessary corrections"""
        valid_response = json.dumps({
            "pattern_name": "Valid Pattern",
            "entry_conditions": [{"condition": "close_above_high", "lookback": 1}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })
        
        corrected, patterns, was_corrected = self.validator.validate_and_correct(
            self.mock_client, "original prompt", valid_response
        )
        
        self.assertFalse(was_corrected, "Valid response should not require correction")
        self.assertEqual(len(patterns), 1, "Should parse exactly 1 pattern")
        self.assertEqual(self.mock_client.call_count, 0, "Should not call LLM for valid response")
    
    def test_invalid_json_successful_correction(self):
        """Test successful correction of invalid JSON"""
        correction_response = json.dumps({
            "pattern_name": "Corrected Pattern",
            "entry_conditions": [{"condition": "range_expansion", "threshold": 1.5}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 3}]
        })
        
        mock_client = MockLLMClient([correction_response])
        invalid_response = "This is not JSON at all"
        
        corrected, patterns, was_corrected = self.validator.validate_and_correct(
            mock_client, "original prompt", invalid_response
        )
        
        self.assertTrue(was_corrected, "Invalid response should require correction")
        self.assertEqual(len(patterns), 1, "Should parse exactly 1 pattern after correction")
        self.assertEqual(mock_client.call_count, 1, "Should make exactly 1 correction call")
    
    def test_multiple_correction_attempts(self):
        """Test multiple correction attempts with eventual success"""
        first_attempt = "Still not valid JSON"
        second_attempt = json.dumps({
            "pattern_name": "Finally Corrected Pattern",
            "entry_conditions": [{"condition": "consecutive_days", "periods": 3}],
            "exit_conditions": [{"condition": "fixed_take_profit", "percentage": 0.02}]
        })
        
        mock_client = MockLLMClient([first_attempt, second_attempt])
        invalid_response = "Original invalid response"
        
        corrected, patterns, was_corrected = self.validator.validate_and_correct(
            mock_client, "original prompt", invalid_response
        )
        
        self.assertTrue(was_corrected, "Invalid response should require correction")
        self.assertEqual(len(patterns), 1, "Should parse exactly 1 pattern after correction")
        self.assertEqual(mock_client.call_count, 2, "Should make exactly 2 correction calls")
    
    def test_all_correction_attempts_fail(self):
        """Test behavior when all correction attempts fail"""
        mock_client = MockLLMClient([
            "First correction attempt - still invalid",
            "Second correction attempt - still invalid"
        ])
        
        invalid_response = "Original invalid response"
        
        with self.assertRaises(BacktestingRuleParseError) as context:
            self.validator.validate_and_correct(mock_client, "original prompt", invalid_response)
        
        self.assertIn("correction attempts", str(context.exception))
        self.assertEqual(mock_client.call_count, 2, "Should make exactly 2 correction calls before failing")
    
    def test_error_categorization(self):
        """Test error categorization functionality"""
        test_cases = [
            ("Not JSON", "not_json_format"),
            ('{"pattern_name": "test"', "malformed_json"),
            ('{"entry_conditions": []}', "missing_pattern_name"),
            ('{"pattern_name": "test"}', "missing_entry_conditions"),
        ]
        
        for invalid_input, expected_category in test_cases:
            try:
                self.validator.parser.parse_llm_response(invalid_input)
            except Exception as e:
                category = self.validator._categorize_error(e, invalid_input)
                # Verify categorization is working (exact match not required due to nuanced categorization)
                self.assertIsInstance(category, str)
                self.assertTrue(len(category) > 0)
    
    def test_statistics_tracking(self):
        """Test statistics tracking functionality"""
        # Valid response
        valid_response = json.dumps({
            "pattern_name": "Valid Pattern",
            "entry_conditions": [{"condition": "close_above_high"}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })
        
        self.validator.validate_and_correct(self.mock_client, "prompt", valid_response)
        
        # Invalid response with successful correction
        mock_client = MockLLMClient([valid_response])
        self.validator.validate_and_correct(mock_client, "prompt", "invalid")
        
        stats = self.validator.get_statistics()
        self.assertEqual(stats['total_validations'], 2)
        self.assertEqual(stats['successful_first_attempt'], 1)
        self.assertEqual(stats['successful_after_correction'], 1)
        self.assertGreater(stats['overall_success_rate'], 0)
    
    def test_correction_prompt_quality(self):
        """Test the quality and specificity of correction prompts"""
        test_cases = [
            ("Not JSON at all", "not_json_format"),
            ('{"pattern_name": "test"', "malformed_json"),
            ('{"entry_conditions": []}', "missing_pattern_name"),
        ]
        
        for invalid_input, expected_category in test_cases:
            try:
                self.validator.parser.parse_llm_response(invalid_input)
            except Exception as e:
                category = self.validator._categorize_error(e, invalid_input)
                prompt = self.validator._generate_correction_prompt(
                    "original prompt", invalid_input, category, 1
                )
                
                # Verify prompt quality
                self.assertGreater(len(prompt), 200, "Correction prompt should be detailed")
                self.assertIn("SPECIFIC ISSUE:", prompt, "Should contain specific issue identification")
                self.assertIn("REQUIRED FIX:", prompt, "Should contain specific fix instructions")
                self.assertIn(invalid_input, prompt, "Should include the failed response")
    
    def test_json_extraction_from_markdown(self):
        """Test JSON extraction from markdown code blocks"""
        valid_json = json.dumps({
            "pattern_name": "Test Pattern",
            "entry_conditions": [{"condition": "close_above_high", "lookback": 1}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })

        # Test with json specifier
        markdown_response = f"Here's the pattern:\n\n```json\n{valid_json}\n```\n\nThis should work."
        extracted = self.validator._extract_json_from_markdown(markdown_response)
        self.assertNotEqual(extracted, markdown_response)

        # Test without json specifier
        markdown_response2 = f"```\n{valid_json}\n```"
        extracted2 = self.validator._extract_json_from_markdown(markdown_response2)
        self.assertNotEqual(extracted2, markdown_response2)

        # Test with no markdown (should return original)
        plain_text = "No JSON here"
        extracted3 = self.validator._extract_json_from_markdown(plain_text)
        self.assertEqual(extracted3, plain_text)

    def test_validator_initialization_parameters(self):
        """Test validator initialization with different parameters"""
        validator = LLMResponseValidator(max_retries=5, retry_delay=2.0)
        self.assertEqual(validator.max_retries, 5)
        self.assertEqual(validator.retry_delay, 2.0)
        self.assertIsNotNone(validator.parser)
        self.assertEqual(validator.stats['total_validations'], 0)

    def test_statistics_reset(self):
        """Test statistics reset functionality"""
        # Perform some validations first
        valid_response = json.dumps({
            "pattern_name": "Test",
            "entry_conditions": [{"condition": "close_above_high"}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })
        self.validator.validate_and_correct(self.mock_client, "prompt", valid_response)

        # Verify stats are not zero
        self.assertGreater(self.validator.stats['total_validations'], 0)

        # Reset and verify
        self.validator.reset_statistics()
        self.assertEqual(self.validator.stats['total_validations'], 0)
        self.assertEqual(self.validator.stats['successful_first_attempt'], 0)

    def test_llm_client_response_formats(self):
        """Test handling of different LLM client response formats"""
        from unittest.mock import Mock

        valid_json = json.dumps({
            "pattern_name": "Test",
            "entry_conditions": [{"condition": "close_above_high"}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })

        # Test dict response format
        mock_client1 = Mock()
        mock_client1.send_message.return_value = {'response': valid_json}

        response, patterns, was_corrected = self.validator.validate_and_correct(
            mock_client1, "prompt", "invalid"
        )
        self.assertIsInstance(patterns, list)

        # Test string response format
        mock_client2 = Mock()
        mock_client2.send_message.return_value = valid_json

        response, patterns, was_corrected = self.validator.validate_and_correct(
            mock_client2, "prompt", "invalid"
        )
        self.assertIsInstance(patterns, list)

    def test_error_category_edge_cases(self):
        """Test error categorization for various edge cases"""
        test_cases = [
            (Exception("JSON decode error"), "json_parse_error"),
            (Exception("missing required field pattern_name"), "missing_pattern_name"),
            (Exception("missing required field entry_conditions"), "missing_entry_conditions"),
            (Exception("missing required field exit_conditions"), "missing_exit_conditions"),
            (Exception("invalid condition type"), "invalid_condition_type"),
            (Exception("no patterns found"), "no_patterns_found"),
            (Exception("some other error"), "unknown_error"),
        ]

        for error, expected_category in test_cases:
            category = self.validator._categorize_error(error, "test response")
            self.assertIsInstance(category, str)
            self.assertGreater(len(category), 0)

    def test_correction_prompt_all_categories(self):
        """Test correction prompt generation for all error categories"""
        categories = [
            "not_json_format", "malformed_json", "missing_pattern_name",
            "missing_entry_conditions", "missing_exit_conditions",
            "invalid_condition_type", "no_patterns_found", "unknown_error"
        ]

        for category in categories:
            prompt = self.validator._generate_correction_prompt(
                "original prompt", "failed response", category, 1
            )

            # Verify prompt structure
            self.assertGreater(len(prompt), 200)
            self.assertIn("FORMAT ERROR DETECTED", prompt)
            self.assertIn("failed response", prompt)
            self.assertIn("CORRECTED JSON:", prompt)

    def test_update_error_stats(self):
        """Test error statistics update functionality"""
        # Test new error category
        self.validator._update_error_stats("test_error")
        self.assertEqual(self.validator.stats['error_categories']['test_error'], 1)

        # Test incrementing existing category
        self.validator._update_error_stats("test_error")
        self.assertEqual(self.validator.stats['error_categories']['test_error'], 2)

    def test_statistics_with_zero_validations(self):
        """Test statistics calculation with zero validations"""
        validator = LLMResponseValidator()
        stats = validator.get_statistics()

        self.assertEqual(stats['total_validations'], 0)
        self.assertEqual(stats['successful_first_attempt'], 0)
        self.assertEqual(stats['successful_after_correction'], 0)
        self.assertEqual(stats['failed_validations'], 0)

    def test_multiple_json_objects_extraction(self):
        """Test extraction of multiple JSON objects from markdown"""
        json1 = '{"pattern_name": "Pattern1", "entry_conditions": [], "exit_conditions": []}'
        json2 = '{"pattern_name": "Pattern2", "entry_conditions": [], "exit_conditions": []}'

        markdown_response = f"""
Here are two patterns:

```json
{json1}
```

And another one:

```json
{json2}
```
"""

        extracted = self.validator._extract_json_from_markdown(markdown_response)
        self.assertNotEqual(extracted, markdown_response)
        # Should be able to parse the extracted JSON
        try:
            parsed = json.loads(extracted)
            self.assertIsInstance(parsed, (list, dict))
        except json.JSONDecodeError:
            self.fail("Extracted JSON should be valid")

    def test_retry_delay_application(self):
        """Test that retry delay is applied correctly"""
        from unittest.mock import patch

        # Create validator with specific retry_delay for this test
        test_validator = LLMResponseValidator(max_retries=2, retry_delay=0.1)

        with patch('time.sleep') as mock_sleep:
            # First attempt fails, second attempt succeeds
            mock_client = MockLLMClient([
                "still invalid",  # First correction attempt fails
                json.dumps({
                    "pattern_name": "Test",
                    "entry_conditions": [{"condition": "close_above_high"}],
                    "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
                })
            ])

            test_validator.validate_and_correct(mock_client, "prompt", "invalid")

            # Should have called sleep with retry_delay * attempt (0.1 * 2 = 0.2)
            mock_sleep.assert_called_with(0.2)

    def test_json_extraction_from_markdown_success(self):
        """Test successful JSON extraction from markdown code blocks"""
        # Create a response that will fail the parser's initial attempt but succeed with validator extraction
        # Use a format that the parser's simple regex won't catch but the validator's more sophisticated logic will
        markdown_response = """Here's the pattern you requested:

```json

{
    "pattern_name": "Extracted Pattern",
    "entry_conditions": [{"condition": "close_above_high"}],
    "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
}

```

This pattern should work well for your trading strategy!"""

        # Mock the parser to fail on first attempt but succeed on extracted JSON
        original_parse = self.validator.parser.parse_llm_response
        call_count = 0

        def mock_parse(response):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                # First call (original response) should fail
                raise Exception("Initial parsing failed")
            else:
                # Second call (extracted JSON) should succeed
                return original_parse(response)

        self.validator.parser.parse_llm_response = mock_parse

        corrected, patterns, was_corrected = self.validator.validate_and_correct(
            self.mock_client, "prompt", markdown_response
        )

        self.assertTrue(was_corrected, "Should be marked as corrected since JSON was extracted")
        self.assertEqual(len(patterns), 1, "Should parse one pattern from extracted JSON")
        self.assertEqual(patterns[0].pattern_name, "Extracted Pattern")

        # Restore original method
        self.validator.parser.parse_llm_response = original_parse

    def test_json_extraction_with_invalid_objects(self):
        """Test JSON extraction that skips invalid JSON objects"""
        # Create a response with mixed valid/invalid JSON that will trigger extraction logic
        mixed_response = """Here are the patterns I found:

First pattern:
```json
{
    "pattern_name": "Valid Pattern",
    "entry_conditions": [{"condition": "close_above_high"}],
    "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
}
```

Second pattern (this one has issues):
```json
{
    "pattern_name": "Invalid Pattern",
    "entry_conditions": [{"condition": "close_above_high"}],
    "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}],
    "invalid_json": "missing closing brace"
```

Hope this helps!"""

        # Mock the parser to fail on first attempt but succeed on extracted JSON
        original_parse = self.validator.parser.parse_llm_response
        call_count = 0

        def mock_parse(response):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                # First call (original response) should fail
                raise Exception("Initial parsing failed")
            else:
                # Second call (extracted JSON) should succeed - only valid JSON should remain
                return original_parse(response)

        self.validator.parser.parse_llm_response = mock_parse

        # This should extract the valid JSON and skip the invalid one
        corrected, patterns, was_corrected = self.validator.validate_and_correct(
            self.mock_client, "prompt", mixed_response
        )

        self.assertTrue(was_corrected, "Should be marked as corrected since JSON was extracted")
        self.assertEqual(len(patterns), 1, "Should parse one pattern from valid JSON")
        self.assertEqual(patterns[0].pattern_name, "Valid Pattern")

        # Restore original method
        self.validator.parser.parse_llm_response = original_parse

    def test_error_categorization_json_parse_error(self):
        """Test error categorization for generic JSON parse errors"""
        # Create an error that contains 'json' but doesn't match specific patterns
        error = Exception("JSON decode error: unexpected character")
        response = '{"pattern_name": "test"}'  # Starts with { so not 'not_json_format'

        category = self.validator._categorize_error(error, response)
        self.assertEqual(category, 'json_parse_error')

    def test_error_categorization_missing_required_fields(self):
        """Test error categorization for generic missing required fields"""
        # Create an error that contains 'missing' but doesn't match specific field patterns
        error = Exception("Missing required field: some_other_field")
        response = '{"pattern_name": "test"}'

        category = self.validator._categorize_error(error, response)
        self.assertEqual(category, 'missing_required_fields')

    def test_fallback_return_with_zero_retries(self):
        """Test the fallback return case when max_retries is 0"""
        # Create validator with 0 retries to trigger fallback path
        zero_retry_validator = LLMResponseValidator(max_retries=0, retry_delay=0.1)

        # Use invalid response that will fail initial validation
        invalid_response = "This is not JSON"

        # Should reach the fallback return since no retry attempts are made
        corrected, patterns, was_corrected = zero_retry_validator.validate_and_correct(
            self.mock_client, "prompt", invalid_response
        )

        self.assertTrue(was_corrected, "Should be marked as corrected")
        self.assertEqual(len(patterns), 0, "Should return empty patterns list")
        self.assertEqual(corrected, invalid_response, "Should return original response")
        self.assertEqual(zero_retry_validator.stats['failed_validations'], 1, "Should increment failed validations")

    def test_json_extraction_failure_after_initial_failure(self):
        """Test the case where both initial parsing and JSON extraction fail"""
        # Create a response that will fail both initial parsing and extraction
        invalid_response = "This is completely invalid text with no JSON anywhere"

        # Mock the parser to fail only for the original response and extracted JSON
        original_parse = self.validator.parser.parse_llm_response
        call_count = 0

        def mock_parse_selective_fail(response):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                # First two calls (original response and extracted JSON) should fail
                raise Exception("Initial parsing and extraction failed")
            else:
                # Third call (corrected response) should succeed
                return original_parse(response)

        self.validator.parser.parse_llm_response = mock_parse_selective_fail

        # Mock the LLM client to return a valid response for correction
        mock_client = MockLLMClient([json.dumps({
            "pattern_name": "Corrected Pattern",
            "entry_conditions": [{"condition": "close_above_high"}],
            "exit_conditions": [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]
        })])

        # This should trigger the JSON extraction failure path (lines 82-83)
        # and then proceed to correction attempts
        corrected, patterns, was_corrected = self.validator.validate_and_correct(
            mock_client, "prompt", invalid_response
        )

        self.assertTrue(was_corrected, "Should be corrected via LLM correction")
        self.assertEqual(len(patterns), 1, "Should have one corrected pattern")
        self.assertEqual(patterns[0].pattern_name, "Corrected Pattern")

        # Restore original method
        self.validator.parser.parse_llm_response = original_parse

if __name__ == '__main__':
    unittest.main()
