"""Tests for MetricsGenerator module"""

import pytest
import pandas as pd
import unittest
import os
import sys
import importlib.util
from unittest.mock import Mock, MagicMock, patch
import numpy as np

# Add src directory to path
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from metrics_generator import MetricsGenerator


class TestMetricsGenerator(unittest.TestCase):
    """Test suite for MetricsGenerator class"""
    
    def setUp(self):
        """Setup test fixtures"""
        self.path = os.path.join(os.path.dirname(__file__), '../src/metrics_generator.py')
        spec = importlib.util.spec_from_file_location('metrics_generator', self.path)
        self.module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(self.module)
        
        self.generator = MetricsGenerator()
        
        # Mock stats object with comprehensive metrics
        self.mock_stats = Mock()
        self.mock_stats.__getitem__ = Mock(side_effect=self._mock_stats_getitem)
        self.mock_stats.get = Mock(side_effect=self._mock_stats_get)
        self.mock_stats.__contains__ = Mock(return_value=True)
        
        # Mock trades data
        self.mock_trades = [
            Mock(pl=100, pl_pct=5.0, entry_time=pd.Timestamp('2023-01-01'), exit_time=pd.Timestamp('2023-01-02')),
            Mock(pl=-50, pl_pct=-2.5, entry_time=pd.Timestamp('2023-01-03'), exit_time=pd.Timestamp('2023-01-04')),
            Mock(pl=200, pl_pct=10.0, entry_time=pd.Timestamp('2023-01-05'), exit_time=pd.Timestamp('2023-01-06'))
        ]
        
        # Add _trades DataFrame to mock stats
        trades_data = pd.DataFrame({
            'PnL': [100, -50, 200],
            'Entry': pd.to_datetime(['2023-01-01', '2023-01-03', '2023-01-05']),
            'Exit': pd.to_datetime(['2023-01-02', '2023-01-04', '2023-01-06'])
        })
        self.mock_stats._trades = trades_data
        
    def _mock_stats_getitem(self, key):
        """Mock stats dictionary access"""
        stats_data = {
            'Return [%]': 15.5,
            'CAGR [%]': 12.3,
            'Sharpe Ratio': 1.45,
            'Sortino Ratio': 1.8,
            'Calmar Ratio': 0.95,
            'Max Drawdown [%]': -8.2,
            'Avg Drawdown [%]': -3.1,
            'Max Drawdown Duration': 15,
            'Avg Drawdown Duration': 5,
            '# Trades': 150,
            'Win Rate [%]': 65.5,
            'Best Trade [%]': 25.3,
            'Worst Trade [%]': -12.1,
            'Avg Trade [%]': 2.1,
            'Max Trade Duration': 8,
            'Avg Trade Duration': 3,
            'Profit Factor': 1.85,
            'Expectancy [%]': 1.2,
            'SQN': 2.1,
            'Kelly Criterion': 0.15,
            'Start': pd.Timestamp('2023-01-01'),
            'End': pd.Timestamp('2023-12-31'),
            'Duration': pd.Timedelta(days=365),
            'Exposure Time [%]': 45.2,
            'Equity Final [$]': 115500,
            'Equity Peak [$]': 118000,
            'Buy & Hold Return [%]': 8.5,
            'Return (Ann.) [%]': 12.3,
            'Volatility (Ann.) [%]': 18.5,
            '_trades': self.mock_trades
        }
        return stats_data.get(key, 0)
        
    def _mock_stats_get(self, key, default=None):
        """Mock stats get method"""
        return self._mock_stats_getitem(key) or default
    
    def test_module_import(self):
        """Test module import"""
        self.assertTrue(hasattr(self.module, 'MetricsGenerator') or hasattr(self.module, 'main'))

    def test_no_hardcoded_params(self):
        """Test no hardcoded parameters"""
        with open(self.path, 'r') as f:
            content = f.read()
        self.assertNotIn('="', content, msg='UNBREAKABLE RULE VIOLATION: Hardcoded parameter found in metrics_generator.py')
    
    def test_init(self):
        """Test MetricsGenerator initialization"""
        generator = MetricsGenerator()
        self.assertIsNotNone(generator.logger)
        
    def test_generate_comprehensive_metrics_table_basic(self):
        """Test basic metrics table generation"""
        result = self.generator.generate_comprehensive_metrics_table(self.mock_stats)
        
        self.assertIn("## 📊 Trading Metrics", result)
        self.assertIn("### 🎯 Core Performance", result)
        self.assertIn("Total Return", result)
        self.assertIn("15.50%", result)
        self.assertIn("CAGR", result)
        self.assertIn("12.30%", result)
        
    def test_generate_comprehensive_metrics_table_with_rule_id(self):
        """Test metrics table generation with rule ID"""
        result = self.generator.generate_comprehensive_metrics_table(self.mock_stats, rule_id=42)
        
        self.assertIn("### Rule 42 Performance Analysis", result)
        self.assertIn("## 📊 Trading Metrics", result)
        
    def test_extract_all_backtesting_metrics(self):
        """Test extraction of all backtesting metrics"""
        metrics = self.generator._extract_all_backtesting_metrics(self.mock_stats)
        
        self.assertIsInstance(metrics, dict)
        self.assertEqual(metrics['Return [%]'], 15.5)
        self.assertEqual(metrics['CAGR [%]'], 12.3)
        self.assertEqual(metrics['Sharpe Ratio'], 1.45)
        self.assertEqual(metrics['# Trades'], 150)
        
    def test_get_additional_metrics(self):
        """Test calculation of additional metrics"""
        additional_metrics = self.generator._get_additional_metrics(self.mock_stats)
        
        self.assertIsInstance(additional_metrics, list)
        self.assertGreater(len(additional_metrics), 0)
        
        # Check for specific metrics
        metric_names = [metric[0] for metric in additional_metrics]
        self.assertIn("Max Consecutive Wins", metric_names)
        self.assertIn("Max Consecutive Losses", metric_names)
        self.assertIn("Avg Winning Trade", metric_names)
        self.assertIn("Avg Losing Trade", metric_names)
        
    def test_max_consecutive_wins(self):
        """Test max consecutive wins calculation"""
        # Create boolean series for wins
        wins = pd.Series([True, True, False, True, True, True, False])
        result = self.generator._max_consecutive(wins)
        self.assertEqual(result, 3)
        
    def test_max_consecutive_losses(self):
        """Test max consecutive losses calculation"""
        # Create boolean series for losses
        losses = pd.Series([False, False, True, False, False, False, True])
        result = self.generator._max_consecutive(losses)
        self.assertEqual(result, 1)
        
    def test_max_consecutive_empty_series(self):
        """Test max consecutive with empty series"""
        empty_series = pd.Series([], dtype=bool)
        result = self.generator._max_consecutive(empty_series)
        self.assertEqual(result, 0)
        
    def test_calculate_performance_grade(self):
        """Test performance grade calculation"""
        metrics = {
            'Return [%]': 15.5,
            'Sharpe Ratio': 1.45,
            'Max Drawdown [%]': -8.2,
            'Win Rate [%]': 65.5,
            'Profit Factor': 1.85
        }
        
        grade_info = self.generator._calculate_performance_grade(metrics)
        
        self.assertIn('grade', grade_info)
        self.assertIn('score', grade_info)
        self.assertIn('strengths', grade_info)
        self.assertIn('weaknesses', grade_info)
        self.assertIsInstance(grade_info['score'], (int, float))
        self.assertIn(grade_info['grade'], ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D'])
        
    def test_calculate_performance_grade_excellent(self):
        """Test performance grade for excellent metrics"""
        excellent_metrics = {
            'Return [%]': 50.0,
            'Sharpe Ratio': 3.0,
            'Max. Drawdown [%]': 2.0,
            'Win Rate [%]': 85.0,
            'Profit Factor': 4.0
        }
        
        grade_info = self.generator._calculate_performance_grade(excellent_metrics)
        self.assertIn(grade_info['grade'], ['A+', 'A'])
        self.assertGreaterEqual(grade_info['score'], 90)
        
    def test_calculate_performance_grade_poor(self):
        """Test performance grade for poor metrics"""
        poor_metrics = {
            'Return [%]': -10.0,
            'Sharpe Ratio': -0.5,
            'Max Drawdown [%]': -30.0,
            'Win Rate [%]': 30.0,
            'Profit Factor': 0.5
        }
        
        grade_info = self.generator._calculate_performance_grade(poor_metrics)
        self.assertIn(grade_info['grade'], ['D', 'F'])
        self.assertLessEqual(grade_info['score'], 40)
        
    def test_generate_metrics_table_with_missing_stats(self):
        """Test metrics table generation with missing stats"""
        # Create mock stats with missing values
        incomplete_stats = Mock()
        incomplete_stats.__getitem__ = Mock(side_effect=KeyError)
        incomplete_stats.get = Mock(return_value=0)
        incomplete_stats.__contains__ = Mock(return_value=False)
        
        result = self.generator.generate_comprehensive_metrics_table(incomplete_stats)
        
        self.assertIn("## 📊 Trading Metrics", result)
        self.assertIn("0.00%", result)  # Default values should be used
        
    def test_generate_metrics_table_exception_handling(self):
        """Test exception handling in metrics table generation"""
        # Create mock stats that raises exception
        error_stats = Mock()
        error_stats.__getitem__ = Mock(side_effect=Exception("Test error"))
        error_stats.get = Mock(side_effect=Exception("Test error"))
        
        # Should not raise exception, should handle gracefully
        result = self.generator.generate_comprehensive_metrics_table(error_stats)
        self.assertIsInstance(result, str)
        
    def test_additional_metrics_with_no_trades(self):
        """Test additional metrics calculation with no trades"""
        no_trades_stats = Mock()
        no_trades_stats.get = Mock(return_value=[])
        no_trades_stats.__getitem__ = Mock(return_value=[])
        
        additional_metrics = self.generator._get_additional_metrics(no_trades_stats)
        self.assertIsInstance(additional_metrics, list)
        
    def test_metrics_table_formatting(self):
        """Test that metrics table is properly formatted"""
        result = self.generator.generate_comprehensive_metrics_table(self.mock_stats)
        
        # Check markdown table formatting
        self.assertIn("|", result)  # Table separators
        self.assertIn("---", result)  # Table header separators
        self.assertIn("###", result)  # Section headers
        self.assertIn("**", result)  # Bold formatting
        
    def test_comprehensive_metrics_coverage(self):
        """Test that comprehensive metrics include all expected sections"""
        result = self.generator.generate_comprehensive_metrics_table(self.mock_stats)
        
        # Check for all major sections
        expected_sections = [
            "Core Performance",
            "Risk Analysis", 
            "Trading Activity",
            "Time Analysis"
        ]
        
        for section in expected_sections:
            self.assertIn(section, result)
            
    def test_performance_grade_boundary_conditions(self):
        """Test performance grade at boundary conditions"""
        # Test zero values
        zero_metrics = {
            'Return [%]': 0,
            'Sharpe Ratio': 0,
            'Max Drawdown [%]': 0,
            'Win Rate [%]': 0,
            'Profit Factor': 0
        }
        
        grade_info = self.generator._calculate_performance_grade(zero_metrics)
        self.assertIn('grade', grade_info)
        self.assertIsInstance(grade_info['score'], (int, float))
        
    def test_max_consecutive_all_true(self):
        """Test max consecutive with all True values"""
        all_true = pd.Series([True, True, True, True, True])
        result = self.generator._max_consecutive(all_true)
        self.assertEqual(result, 5)
        
    def test_max_consecutive_all_false(self):
        """Test max consecutive with all False values"""
        all_false = pd.Series([False, False, False, False, False])
        result = self.generator._max_consecutive(all_false)
        self.assertEqual(result, 0)


    def test_error_handling_in_comprehensive_metrics(self):
        """Test error handling in generate_comprehensive_metrics_table"""
        # Test with stats that cause AttributeError in _extract_all_backtesting_metrics
        class BadStats:
            def __getattribute__(self, name):
                raise AttributeError("Simulated error")
        
        bad_stats = BadStats()
        result = self.generator.generate_comprehensive_metrics_table(bad_stats)
        # Should handle the error gracefully and return basic metrics table
        self.assertIsInstance(result, str)
        self.assertIn('Trading Metrics', result)

    def test_extract_metrics_error_handling(self):
        """Test error handling in _extract_all_backtesting_metrics"""
        # Test with stats that don't have get method
        class BadStats:
            pass
        
        bad_stats = BadStats()
        metrics = self.generator._extract_all_backtesting_metrics(bad_stats)
        self.assertIsInstance(metrics, dict)
        # Should return 0 for missing metrics
        self.assertEqual(metrics.get('Return [%]', None), 0)

    def test_additional_metrics_error_handling(self):
        """Test error handling in _get_additional_metrics"""
        # Test with stats that raise exceptions
        bad_stats = Mock()
        bad_stats._trades = Mock(side_effect=Exception("Test error"))
        
        additional = self.generator._get_additional_metrics(bad_stats)
        self.assertIsInstance(additional, list)
        # Should handle errors gracefully

    def test_max_consecutive_edge_cases(self):
        """Test _max_consecutive with edge cases"""
        # Empty series
        result = self.generator._max_consecutive([])
        self.assertEqual(result, 0)
        
        # All False
        result = self.generator._max_consecutive([False, False, False])
        self.assertEqual(result, 0)
        
        # All True
        result = self.generator._max_consecutive([True, True, True])
        self.assertEqual(result, 3)
        
        # Mixed pattern
        result = self.generator._max_consecutive([True, True, False, True, True, True, False])
        self.assertEqual(result, 3)

    def test_performance_grade_edge_cases(self):
        """Test _calculate_performance_grade with various scenarios"""
        # Excellent performance
        excellent_metrics = {
            'Return [%]': 25,
            'Sharpe Ratio': 2.5,
            'Win Rate [%]': 75,
            'Max. Drawdown [%]': 3
        }
        grade = self.generator._calculate_performance_grade(excellent_metrics)
        self.assertEqual(grade['grade'], 'A+')
        self.assertIn('Excellent returns', ' '.join(grade['strengths']))
        
        # Poor performance
        poor_metrics = {
            'Return [%]': -10,
            'Sharpe Ratio': -0.5,
            'Win Rate [%]': 30,
            'Max. Drawdown [%]': 40
        }
        grade = self.generator._calculate_performance_grade(poor_metrics)
        self.assertEqual(grade['grade'], 'D')
        self.assertIn('Negative returns', ' '.join(grade['weaknesses']))
        
        # Missing metrics (should handle gracefully)
        empty_metrics = {}
        grade = self.generator._calculate_performance_grade(empty_metrics)
        self.assertIsInstance(grade, dict)
        self.assertIn('grade', grade)

    def test_recovery_factor_calculation(self):
        """Test recovery factor calculation in additional metrics"""
        # Mock stats with equity curve
        stats_with_equity = Mock()
        stats_with_equity.get = Mock(side_effect=lambda key, default=0: {
            'Return [%]': 20,
            'Max. Drawdown [%]': 10
        }.get(key, default))
        
        # Mock equity curve
        equity_df = pd.DataFrame({'Equity': [100, 110, 105, 120]})
        stats_with_equity._equity_curve = equity_df
        stats_with_equity._trades = pd.DataFrame()
        
        additional = self.generator._get_additional_metrics(stats_with_equity)
        self.assertIsInstance(additional, list)
        
        # Check if recovery factor is calculated
        recovery_found = any('Recovery Factor' in str(item) for item in additional)
        self.assertTrue(recovery_found)

    def test_division_by_zero_protection(self):
        """Test protection against division by zero"""
        # Mock stats with zero max drawdown
        stats_zero_dd = Mock()
        stats_zero_dd.get = Mock(side_effect=lambda key, default=0: {
            'Return [%]': 20,
            'Max. Drawdown [%]': 0  # This could cause division by zero
        }.get(key, default))
        stats_zero_dd._equity_curve = pd.DataFrame()
        stats_zero_dd._trades = pd.DataFrame()
        
        # Should not raise exception
        additional = self.generator._get_additional_metrics(stats_zero_dd)
        self.assertIsInstance(additional, list)

    def test_win_loss_ratio_calculation(self):
        """Test win/loss ratio calculation with edge cases"""
        # Mock trades with wins and losses
        trades_df = pd.DataFrame({
            'PnL': [100, -50, 200, -25, 150],
            'Entry': pd.date_range('2023-01-01', periods=5),
            'Exit': pd.date_range('2023-01-02', periods=5)
        })
        
        stats_with_trades = Mock()
        stats_with_trades._trades = trades_df
        stats_with_trades._equity_curve = pd.DataFrame()
        stats_with_trades.get = Mock(return_value=0)
        
        additional = self.generator._get_additional_metrics(stats_with_trades)
        self.assertIsInstance(additional, list)
        
        # Check if win/loss ratio is calculated
        ratio_found = any('Win/Loss Ratio' in str(item) for item in additional)
        self.assertTrue(ratio_found)
    
    def test_generate_metrics_table_exception_handling(self):
        """Test exception handling in generate_comprehensive_metrics_table"""
        # Mock the _extract_all_backtesting_metrics method to raise an exception
        with patch.object(self.generator, '_extract_all_backtesting_metrics', side_effect=Exception("Test exception")):
            result = self.generator.generate_comprehensive_metrics_table(self.mock_stats)
            
            self.assertIn("❌ **Error generating metrics**", result)
            self.assertIn("Test exception", result)
    
    def test_extract_metrics_exception_handling(self):
        """Test exception handling in _extract_all_backtesting_metrics"""
        # Create a mock stats object that raises exceptions
        bad_stats = Mock()
        bad_stats.__getitem__ = Mock(side_effect=Exception("Bad access"))
        bad_stats.get = Mock(side_effect=Exception("Bad get"))
        
        # Should handle exceptions gracefully
        metrics = self.generator._extract_all_backtesting_metrics(bad_stats)
        self.assertIsInstance(metrics, dict)
        # Should have default values for failed metrics
        self.assertEqual(metrics.get('Return [%]', None), 0)
    
    def test_extract_metrics_missing_attributes(self):
        """Test handling of missing attributes in stats object"""
        # Create a stats object that doesn't have expected attributes
        stats_no_attrs = Mock()
        stats_no_attrs.__getitem__ = Mock(return_value=10.5)  # Has basic access
        stats_no_attrs.get = Mock(return_value=5.0)  # Has get method
        
        # Mock hasattr to return False for some attributes
        with patch('builtins.hasattr', return_value=False):
            metrics = self.generator._extract_all_backtesting_metrics(stats_no_attrs)
            self.assertIsInstance(metrics, dict)
            # Should have default values for missing attributes
            self.assertIn('Return [%]', metrics)
    
    def test_additional_metrics_empty_trades(self):
        """Test additional metrics with empty trades DataFrame"""
        stats_empty_trades = Mock()
        stats_empty_trades._trades = pd.DataFrame()  # Empty DataFrame
        stats_empty_trades._equity_curve = pd.DataFrame()
        stats_empty_trades.get = Mock(return_value=0)
        
        additional = self.generator._get_additional_metrics(stats_empty_trades)
        self.assertIsInstance(additional, list)
    
    def test_additional_metrics_no_winning_trades(self):
        """Test additional metrics when there are no winning trades"""
        # All losing trades
        trades_df = pd.DataFrame({
            'PnL': [-50, -25, -100],
            'Entry': pd.date_range('2023-01-01', periods=3),
            'Exit': pd.date_range('2023-01-02', periods=3)
        })
        
        stats_no_wins = Mock()
        stats_no_wins._trades = trades_df
        stats_no_wins._equity_curve = pd.DataFrame()
        stats_no_wins.get = Mock(return_value=0)
        
        additional = self.generator._get_additional_metrics(stats_no_wins)
        self.assertIsInstance(additional, list)
        
        # Should have avg losing trade but no avg winning trade or win/loss ratio
        metric_names = [metric[0] for metric in additional]
        self.assertIn("Avg Losing Trade", metric_names)
        self.assertNotIn("Avg Winning Trade", metric_names)
        self.assertNotIn("Win/Loss Ratio", metric_names)
    
    def test_additional_metrics_no_losing_trades(self):
        """Test additional metrics when there are no losing trades"""
        # All winning trades
        trades_df = pd.DataFrame({
            'PnL': [50, 25, 100],
            'Entry': pd.date_range('2023-01-01', periods=3),
            'Exit': pd.date_range('2023-01-02', periods=3)
        })
        
        stats_no_losses = Mock()
        stats_no_losses._trades = trades_df
        stats_no_losses._equity_curve = pd.DataFrame()
        stats_no_losses.get = Mock(return_value=0)
        
        additional = self.generator._get_additional_metrics(stats_no_losses)
        self.assertIsInstance(additional, list)
        
        # Should have avg winning trade but no avg losing trade or win/loss ratio
        metric_names = [metric[0] for metric in additional]
        self.assertIn("Avg Winning Trade", metric_names)
        self.assertNotIn("Avg Losing Trade", metric_names)
        self.assertNotIn("Win/Loss Ratio", metric_names)
    
    def test_performance_grade_poor_metrics(self):
        """Test performance grade for poor metrics"""
        poor_metrics = {
            'Return [%]': -10.0,
            'Sharpe Ratio': -0.5,
            'Max. Drawdown [%]': 40.0,
            'Win Rate [%]': 25.0,
            'Profit Factor': 0.5
        }
        
        grade_info = self.generator._calculate_performance_grade(poor_metrics)
        self.assertEqual(grade_info['grade'], 'D')
        self.assertLess(grade_info['score'], 40)
        self.assertIn('weaknesses', grade_info)
        self.assertGreater(len(grade_info['weaknesses']), 0)
        # Check that negative returns weakness is included
        self.assertIn('Negative returns', grade_info['weaknesses'])
    
    def test_performance_grade_boundary_conditions(self):
        """Test performance grade boundary conditions"""
        # Test each grade boundary with realistic scoring
        test_cases = [
            # A+ grade (90+ points): 25+25+25+25 = 100 points
            ({'Return [%]': 25, 'Sharpe Ratio': 2.5, 'Max. Drawdown [%]': 3, 'Win Rate [%]': 70, 'Profit Factor': 3}, 'A+'),
            # A grade (80-89 points): 25+20+20+20 = 85 points  
            ({'Return [%]': 25, 'Sharpe Ratio': 1.5, 'Max. Drawdown [%]': 8, 'Win Rate [%]': 55, 'Profit Factor': 2.5}, 'A'),
            # B+ grade (70-79 points): 20+20+15+15 = 70 points
            ({'Return [%]': 15, 'Sharpe Ratio': 1.5, 'Max. Drawdown [%]': 15, 'Win Rate [%]': 45, 'Profit Factor': 2.0}, 'B+'),
            # B grade (60-69 points): 20+15+15+15 = 65 points
            ({'Return [%]': 15, 'Sharpe Ratio': 0.8, 'Max. Drawdown [%]': 15, 'Win Rate [%]': 45, 'Profit Factor': 1.8}, 'B'),
            # C+ grade (50-59 points): 15+15+15+10 = 55 points
            ({'Return [%]': 8, 'Sharpe Ratio': 0.8, 'Max. Drawdown [%]': 15, 'Win Rate [%]': 35, 'Profit Factor': 1.3}, 'C+'),
            # C grade (40-49 points): 15+10+10+10 = 45 points
            ({'Return [%]': 8, 'Sharpe Ratio': 0.3, 'Max. Drawdown [%]': 25, 'Win Rate [%]': 35, 'Profit Factor': 1.1}, 'C')
        ]
        
        for metrics, expected_grade in test_cases:
            grade_info = self.generator._calculate_performance_grade(metrics)
            self.assertEqual(grade_info['grade'], expected_grade, f"Failed for metrics: {metrics}, got {grade_info['grade']} with score {grade_info['score']}")
    
    def test_extract_all_backtesting_metrics_exception_handling(self):
        """Test exception handling in _extract_all_backtesting_metrics (covers lines 146-147)"""
        # Create a stats object that will raise an exception
        class BadStats:
            def get(self, key, default=None):
                raise Exception("Test exception")
        
        bad_stats = BadStats()
        metrics = self.generator._extract_all_backtesting_metrics(bad_stats)
        
        # Should handle exception gracefully and return 0 for metrics
        self.assertIsInstance(metrics, dict)
        for key, value in metrics.items():
            self.assertEqual(value, 0)
            
    def test_extract_all_backtesting_metrics_no_attributes(self):
        """Test handling when stats object has no required attributes (covers line 148)"""
        # Create object with no relevant attributes
        empty_stats = MagicMock()
        empty_stats.get = None  # No get method
        
        def mock_hasattr(obj, attr):
            return False  # No attributes found
        
        with patch('builtins.hasattr', side_effect=mock_hasattr):
            metrics = self.generator._extract_all_backtesting_metrics(empty_stats)
        
        # Should return 0 for all metrics when no attributes found
        self.assertIsInstance(metrics, dict)
        for key, value in metrics.items():
            self.assertEqual(value, 0)

if __name__ == '__main__':
    unittest.main()
