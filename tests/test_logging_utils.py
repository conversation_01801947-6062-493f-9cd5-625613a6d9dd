#!/usr/bin/env python3
"""
Tests for logging_utils.py module.
Comprehensive test coverage for all logging utility functions.
"""

import unittest
import logging
import sys
import os
import tempfile
from unittest.mock import patch, MagicMock, mock_open
from io import StringIO

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import logging_utils
from config import config


class TestLoggingUtils(unittest.TestCase):
    """Test cases for logging utilities"""

    def setUp(self):
        """Set up test fixtures"""
        # Clear logger cache before each test
        logging_utils._loggers.clear()
        
        # Reset logging configuration
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

    def tearDown(self):
        """Clean up after tests"""
        # Clear logger cache
        logging_utils._loggers.clear()
        
        # Reset logging configuration
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

    def test_get_logger_basic(self):
        """Test basic logger creation"""
        logger = logging_utils.get_logger('test_logger')
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, 'test_logger')
        self.assertIn('test_logger', logging_utils._loggers)

    def test_get_logger_caching(self):
        """Test logger caching functionality"""
        logger1 = logging_utils.get_logger('cached_logger')
        logger2 = logging_utils.get_logger('cached_logger')
        
        # Should return the same instance
        self.assertIs(logger1, logger2)
        self.assertEqual(len(logging_utils._loggers), 1)

    @patch('config.config.LOG_LEVEL', 'DEBUG')
    @patch('config.config.LOG_TO_FILE', True)
    @patch('config.config.LOG_FILE', 'test.log')
    @patch('config.config.LOG_TO_CONSOLE', True)
    def test_get_logger_with_handlers(self):
        """Test logger creation with file and console handlers"""
        with patch('logging.FileHandler') as mock_file_handler:
            mock_file_handler.return_value = MagicMock()
            
            logger = logging_utils.get_logger('handler_test')
            
            # Should have created file handler
            mock_file_handler.assert_called_once_with('test.log')
            
            # Should have handlers configured
            self.assertTrue(len(logger.handlers) > 0)

    @patch('config.config.LOG_TO_FILE', False)
    @patch('config.config.LOG_TO_CONSOLE', False)
    def test_get_logger_no_handlers(self):
        """Test logger creation with no handlers"""
        logger = logging_utils.get_logger('no_handlers')
        
        # Should still create logger but with no handlers initially
        self.assertIsInstance(logger, logging.Logger)

    def test_log_info(self):
        """Test log_info function"""
        with patch.object(logging_utils, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logging_utils.log_info("Test info message")
            
            mock_get_logger.assert_called_once_with('jaeger')
            mock_logger.info.assert_called_once_with("Test info message")

    def test_log_info_with_custom_logger(self):
        """Test log_info with custom logger name"""
        with patch.object(logging_utils, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logging_utils.log_info("Test message", "custom_logger")
            
            mock_get_logger.assert_called_once_with('custom_logger')
            mock_logger.info.assert_called_once_with("Test message")

    def test_log_warning(self):
        """Test log_warning function"""
        with patch.object(logging_utils, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logging_utils.log_warning("Test warning message")
            
            mock_get_logger.assert_called_once_with('jaeger')
            mock_logger.warning.assert_called_once_with("Test warning message")

    def test_log_error(self):
        """Test log_error function"""
        with patch.object(logging_utils, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logging_utils.log_error("Test error message")
            
            mock_get_logger.assert_called_once_with('jaeger')
            mock_logger.error.assert_called_once_with("Test error message")

    def test_log_debug(self):
        """Test log_debug function"""
        with patch.object(logging_utils, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logging_utils.log_debug("Test debug message")
            
            mock_get_logger.assert_called_once_with('jaeger')
            mock_logger.debug.assert_called_once_with("Test debug message")

    def test_log_critical(self):
        """Test log_critical function"""
        with patch.object(logging_utils, 'get_logger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            logging_utils.log_critical("Test critical message")
            
            mock_get_logger.assert_called_once_with('jaeger')
            mock_logger.critical.assert_called_once_with("Test critical message")

    def test_log_system_start(self):
        """Test log_system_start convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_system_start("TestComponent")
            
            mock_log_info.assert_called_once_with("🚀 TestComponent - Starting...")

    def test_log_system_complete(self):
        """Test log_system_complete convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_system_complete("TestComponent")
            
            mock_log_info.assert_called_once_with("✅ TestComponent - Completed successfully")

    def test_log_system_error(self):
        """Test log_system_error convenience function"""
        with patch.object(logging_utils, 'log_error') as mock_log_error:
            logging_utils.log_system_error("TestComponent", "Test error")
            
            mock_log_error.assert_called_once_with("❌ TestComponent - Error: Test error")

    def test_log_data_processing(self):
        """Test log_data_processing convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_data_processing("Processing data")
            
            mock_log_info.assert_called_once_with("📊 DATA: Processing data")

    def test_log_llm_interaction(self):
        """Test log_llm_interaction convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_llm_interaction("LLM response received")
            
            mock_log_info.assert_called_once_with("🤖 LLM: LLM response received")

    def test_log_backtesting(self):
        """Test log_backtesting convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_backtesting("Backtest completed")
            
            mock_log_info.assert_called_once_with("📈 BACKTEST: Backtest completed")

    def test_log_pattern_discovery(self):
        """Test log_pattern_discovery convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_pattern_discovery("Pattern found")
            
            mock_log_info.assert_called_once_with("🔍 PATTERN: Pattern found")

    def test_log_file_generation(self):
        """Test log_file_generation convenience function"""
        with patch.object(logging_utils, 'log_info') as mock_log_info:
            logging_utils.log_file_generation("File created")
            
            mock_log_info.assert_called_once_with("📁 FILE: File created")

    def test_replace_print_with_logging_default(self):
        """Test replace_print_with_logging with default parameters"""
        result = logging_utils.replace_print_with_logging("Test message")
        
        expected = 'log_info(f"SYSTEM: Test message")'
        self.assertEqual(result, expected)

    def test_replace_print_with_logging_custom(self):
        """Test replace_print_with_logging with custom parameters"""
        result = logging_utils.replace_print_with_logging(
            "Error occurred", 
            level="ERROR", 
            component="CORTEX"
        )
        
        expected = 'log_error(f"CORTEX: Error occurred")'
        self.assertEqual(result, expected)

    def test_replace_print_with_logging_all_levels(self):
        """Test replace_print_with_logging with all log levels"""
        test_cases = [
            ("INFO", "log_info"),
            ("WARNING", "log_warning"),
            ("ERROR", "log_error"),
            ("DEBUG", "log_debug"),
            ("CRITICAL", "log_critical"),
            ("UNKNOWN", "log_info")  # Should default to info
        ]
        
        for level, expected_func in test_cases:
            result = logging_utils.replace_print_with_logging("Test", level=level)
            self.assertIn(expected_func, result)

    def test_categorize_print_statement_error(self):
        """Test categorize_print_statement for error patterns"""
        test_cases = [
            "❌ Error occurred",
            "ERROR: Something went wrong",
            "FAIL: Test failed",
            "Exception caught",
            "Error in processing"
        ]
        
        for case in test_cases:
            result = logging_utils.categorize_print_statement(case)
            self.assertEqual(result, "ERROR")

    def test_categorize_print_statement_warning(self):
        """Test categorize_print_statement for warning patterns"""
        test_cases = [
            "⚠️ Warning message",
            "WARNING: Potential issue",
            "WARN: Check this",
            "This might be a problem",
            "Potential issue detected"
        ]
        
        for case in test_cases:
            result = logging_utils.categorize_print_statement(case)
            self.assertEqual(result, "WARNING")

    def test_categorize_print_statement_success(self):
        """Test categorize_print_statement for success patterns"""
        test_cases = [
            "✅ Success",
            "SUCCESS: Operation completed",
            "COMPLETE: Task finished",
            "DONE processing",
            "FINISHED successfully"
        ]
        
        for case in test_cases:
            result = logging_utils.categorize_print_statement(case)
            self.assertEqual(result, "SUCCESS")

    def test_categorize_print_statement_info(self):
        """Test categorize_print_statement for info patterns"""
        test_cases = [
            "📊 Data processed",
            "🔍 Searching for patterns",
            "🤖 LLM response",
            "📈 Backtest results",
            "📁 File created",
            "INFO: General information"
        ]
        
        for case in test_cases:
            result = logging_utils.categorize_print_statement(case)
            self.assertEqual(result, "INFO")

    def test_categorize_print_statement_debug(self):
        """Test categorize_print_statement for debug patterns"""
        test_cases = [
            "DEBUG: Detailed data",  # Avoid "information" which contains "info"
            "TRACE: Function called",
            "VERBOSE: Extra details"
        ]

        for case in test_cases:
            result = logging_utils.categorize_print_statement(case)
            self.assertEqual(result, "DEBUG")

    def test_categorize_print_statement_default(self):
        """Test categorize_print_statement default behavior"""
        result = logging_utils.categorize_print_statement("Just a regular message")
        self.assertEqual(result, "INFO")

    def test_print_patterns_constant(self):
        """Test PRINT_PATTERNS constant structure"""
        self.assertIsInstance(logging_utils.PRINT_PATTERNS, dict)
        
        expected_keys = ["error", "warning", "success", "info", "debug"]
        for key in expected_keys:
            self.assertIn(key, logging_utils.PRINT_PATTERNS)
            self.assertIsInstance(logging_utils.PRINT_PATTERNS[key], list)


if __name__ == '__main__':
    unittest.main()
