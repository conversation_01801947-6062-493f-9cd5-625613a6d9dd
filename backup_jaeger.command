#!/bin/bash

# Jaeger Backup Script - Double-Click to Run
# Designed for single-user, non-coder operation

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🛡️  JAEGER BACKUP SYSTEM"
echo "========================"
echo "📦 Creating complete backup of your Jaeger system..."
echo ""

# Create backup directory with timestamp
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="jaeger_backup_$BACKUP_DATE"
JAEGER_BACKUP="/Users/<USER>/JaegerBackups/$BACKUP_DIR"

echo "📁 Backup location: $JAEGER_BACKUP"
echo ""

# Create backup directory
mkdir -p "$JAEGER_BACKUP"

# Function to copy with progress
copy_with_progress() {
    local source="$1"
    local dest="$2"
    local name="$3"

    if [ -e "$source" ]; then
        echo "📋 Backing up: $name"
        cp -R "$source" "$dest/"
        echo "   ✅ Complete"
    else
        echo "   ⚠️  Not found: $name"
    fi
}

# Backup core system files
echo "🔧 Backing up core system..."
copy_with_progress "src" "$JAEGER_BACKUP" "Source code"
copy_with_progress "bin" "$JAEGER_BACKUP" "Scripts"
copy_with_progress "docs" "$JAEGER_BACKUP" "Documentation"
copy_with_progress "branding" "$JAEGER_BACKUP" "Branding assets (CRITICAL)"
copy_with_progress "requirements.txt" "$JAEGER_BACKUP" "Dependencies"
copy_with_progress "README.md" "$JAEGER_BACKUP" "Main README"

# Backup executable files
echo ""
echo "🚀 Backing up executable files..."
copy_with_progress "run_jaeger.command" "$JAEGER_BACKUP" "Main launcher"
copy_with_progress "backup_jaeger.command" "$JAEGER_BACKUP" "Backup script"
# Restore script removed for safety - prevents accidental overwrites

# Backup data and results (if they exist)
echo ""
echo "📊 Backing up data and results..."
copy_with_progress "data" "$JAEGER_BACKUP" "Market data"
copy_with_progress "results" "$JAEGER_BACKUP" "Generated trading systems"

# Backup virtual environment info (not the full env, just requirements)
echo ""
echo "🐍 Backing up Python environment info..."
if [ -f "llm_env/pyvenv.cfg" ]; then
    mkdir -p "$JAEGER_BACKUP/env_info"
    cp "llm_env/pyvenv.cfg" "$JAEGER_BACKUP/env_info/"
    echo "   ✅ Environment config saved"
fi

# Create backup manifest
echo ""
echo "📋 Creating backup manifest..."
cat > "$JAEGER_BACKUP/BACKUP_INFO.txt" << EOF
JAEGER BACKUP INFORMATION
========================
Backup Date: $(date)
Backup Location: $JAEGER_BACKUP
Original Location: $SCRIPT_DIR

CONTENTS:
- Source code (src/)
- Scripts (bin/)
- Documentation (docs/)
- Branding assets (branding/) - CRITICAL FILES
- Main launcher (run_jaeger.command)
- Backup script
- Market data (data/)
- Generated results (results/)
- Dependencies (requirements.txt)

RESTORE INSTRUCTIONS:
1. Manually copy files from backup to desired location
2. Run ./bin/setup.sh to reinstall dependencies
3. System will be restored to working condition

IMPORTANT:
- Keep branding/ folder intact - required for operation
- This backup is complete and self-contained
- Safe to move to external drive or cloud storage

Created by: Jaeger Backup System
For: Single-user, non-coder operation
EOF

# Note: Restore script removed for safety to prevent accidental overwrites

# Final summary
echo ""
echo "✅ BACKUP COMPLETE!"
echo "=================="
echo "📁 Backup saved to: $JAEGER_BACKUP"
echo "📦 Backup size: $(du -sh "$JAEGER_BACKUP" | cut -f1)"
echo ""
echo "🛡️  Your backup includes:"
echo "   ✅ Complete Jaeger system"
echo "   ✅ All your data and results"
echo "   ✅ Branding assets (protected)"
echo "   ✅ Self-contained restore script"
echo ""
echo "🔄 To restore:"
echo "   1. Manually copy files from backup folder to desired location"
echo "   2. Run ./bin/setup.sh to reinstall dependencies"
echo ""
echo "💾 Safe to copy backup to external drive or cloud storage"
echo ""
read -p "Press Enter to exit..."
